name: CI

# 1
# Controls when the workflow will run
on:
  # Triggers the workflow on push events but only for the master branch
  push:
    branches: [ staging ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
    inputs:
      version:
        description: 'Build and Deploy'
        required: true
#2
env:
  REGISTRY: "registry.digitalocean.com/heycarsonapp"
  IMAGE_NAME: "app-heycarson-vue"
  CONTAINER_NAME: "vue-app"
#3
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to Digital Ocean droplet via SSH action
        uses: appleboy/ssh-action@v0.1.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          passphrase: ${{ secrets.SSH_PASS }}
          envs: IMAGE_NAME,REGISTRY,CONTAINER_NAME,{{ secrets.DIGITALOCEAN_TOKEN }}
          script: |
            # update project
            cd app-heycarson
            git switch staging
            git fetch origin staging
            git reset --hard origin/staging
            git pull --force origin staging
            
            # clear images
            docker compose down
            docker rmi app-heycarson-vue
            docker rmi app-heycarson-api
            docker system prune -a -f
            docker compose up -d
            
            # init laravel app
            docker exec api-app composer install
            docker exec api-app cp .env.staging .env
            docker exec api-app php artisan key:generate
            docker exec api-app php artisan migrate
            docker exec api-app php artisan db:seed
            docker exec api-app chmod 777 storage/ -R
