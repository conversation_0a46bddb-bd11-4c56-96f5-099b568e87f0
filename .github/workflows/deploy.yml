name: Deploy to Staging

on:
  push:
    branches:
      - staging

concurrency:
  group: staging-deploy
  cancel-in-progress: false

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up SSH access
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known hosts
        run: |
          ssh-keyscan -H *************** >> ~/.ssh/known_hosts

      - name: Deploy latest code
        run: |
          ssh root@*************** "sh ./heycarson-staging-deployment.sh"
