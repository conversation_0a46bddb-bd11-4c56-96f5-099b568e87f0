version: "3.7"
services:
  app:
    restart: always
    build:
      context: ./vue
      dockerfile: DockerLocal.Dockerfile
    image: app-heycarson-vue-local
    container_name: vue-app
    ports:
      - "8080:80"
    environment:
      - "staging"
    networks:
      - app-network

  api:
    build:
      context: ./laravel
    image: app-heycarson-api-local
    container_name: api-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./laravel:/var/www
    environment:
      - APP_ENV=local
    user: "0:0"
    ports:
      - "9000:9000"
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: nginx-app
    ports:
      - "8888:8000"
    volumes:
      - ./:/var/www
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./:/usr/share/nginx/html
    depends_on:
      - api
    networks:
      - app-network

  mysql:
    image: mysql:latest
    container_name: mysql-app
    restart: unless-stopped
    tty: true
    environment:
      MYSQL_ROOT_PASSWORD: App2024Hey<PERSON>arson
      MYSQL_DATABASE: app_heycarson
    volumes:
      - ~/mysql_db/mysql:/var/lib/mysql
      - ~/mysql_config/mysql_local.cnf:/etc/mysql/conf.d/mysql_local.cnf:ro
    networks:
      - app-network
    ports:
      - "33061:3306"

networks:
  app-network:
    driver: bridge

volumes:
  dbdata:
    driver: local

