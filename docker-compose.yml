version: "3.7"
services:
  app:
    restart: always
    build:
      context: ./vue
      dockerfile: DockerStaging.Dockerfile
    image: app-heycarson-vue
    container_name: vue-app
    ports:
      - "80:80"
    environment:
      - "staging"
    networks:
      - app-network

  api:
    build:
      context: ./laravel
    image: app-heycarson-api
    container_name: api-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./laravel:/var/www
    environment:
      - APP_ENV=staging
    user: "0:0"
    ports:
      - "9000:9000"
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: nginx-app
    ports:
      - "8000:8000"
    volumes:
      - ./:/var/www
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./:/usr/share/nginx/html
    depends_on:
      - api
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
