APP_NAME=
APP_ENV=
APP_KEY=
APP_DEBUG=
APP_URL=

LOG_CHANNEL=
LOG_DEPRECATIONS_CHANNEL=
LOG_LEVEL=

DB_CONNECTION=
DB_HOST=
DB_PORT=
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=
DB_SOCKET=

BUGSNAG_API_KEY=

BROADCAST_DRIVER=
CACHE_DRIVER=
FILESYSTEM_DISK=
QUEUE_CONNECTION=
SESSION_DRIVER=
SESSION_LIFETIME=

MEMCACHED_HOST=

REDIS_CLIENT=
REDIS_HOST=
REDIS_PASSWORD=
REDIS_PORT=

MAIL_MAILER=
MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=
MAIL_FROM_ADDRESS=
MAIL_FROM_NAME=

#AWS_ACCESS_KEY_ID=
#AWS_SECRET_ACCESS_KEY=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

AWS_DEFAULT_REGION=
AWS_BUCKET=
AWS_URL=
AWS_USE_PATH_STYLE_ENDPOINT=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=

MIX_PUSHER_APP_KEY=
MIX_PUSHER_APP_CLUSTER=

POSTMARK_TOKEN=
STRIPE_SECRET_KEY=

REVERB_APP_ID=
REVERB_APP_KEY=
REVERB_APP_SECRET=
REVERB_SERVER_HOST=
REVERB_HOST=
REVERB_PORT=
REVERB_SCHEME=

VITE_REVERB_APP_KEY=
VITE_REVERB_HOST=
VITE_REVERB_PORT=
VITE_REVERB_SCHEME=

##<>--- REFERRALS API SOON TO BE USED
# super user
REF_SU_KEY_ID=
REF_SU_SECRET_KEY=

# account user
REF_API_KEY_ID=
REF_API_SECRET_KEY=

# public flow - create click, customer
REF_PUBLIC_KEY=

# ref config
REF_COOKIE_DOMAIN=
REF_ACCOUNT_ID=
REF_API_URL=
REF_INTERNAL_API_URL=
## REFERRALS API SOON TO BE USED --->

PARTNER_AUTH_KEY=
