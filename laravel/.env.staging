APP_NAME=Shopexperts
APP_ENV=staging
APP_KEY=base64:lCZ9+L73/NzkVdYF1a9ojTKwwlNfJhrfjRTMx8zhB4E=
APP_DEBUG=true
APP_URL=http://app.staging.shopexperts.com:8000

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=app_heycarson
DB_USERNAME=heycarson
DB_PASSWORD=AVNS_ZGxoP24TTSZu8DpJrhQ
DB_SOCKET=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=postmark
MAIL_HOST=smtp.postmarkapp.co
MAIL_PORT=587
MAIL_USERNAME=************************************
MAIL_PASSWORD=************************************
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=gx+jLwYEqmjD0TUwBOEvOcHbNlv8N2ta17jHgXcL
AWS_DEFAULT_REGION=eu-north-1
AWS_BUCKET=app-heycarson-staging
AWS_URL=http://s3.eu-north-1.amazonaws.com/app-heycarson-staging
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

POSTMARK_TOKEN=************************************
STRIPE_SECRET_KEY=sk_test_51ISjAdJldSyU9QO2zQcLjECPzh2NvVK6ylkM8SPc3fc7kYQy8mL5A5O37OfSHiRgbBmtASEQGUJI8vWaHGGjcyKQ00hXtSz8sK
