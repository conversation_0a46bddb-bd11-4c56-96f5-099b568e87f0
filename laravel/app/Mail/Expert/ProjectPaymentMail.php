<?php

namespace App\Mail\Expert;

use App\Models\Project;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProjectPaymentMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(public User $user, public User $client, public Project $project)
    {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Nice! A payment has been made!",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            htmlString: "<p>Hi {$this->user->first_name} {$this->user->last_name}</p>
<p>Great news! {$this->client->first_name} {$this->client->last_name} has made a payment for the project: {$this->project->name}.</p>
<p>This is your cue to get started.</p>
<p>Do your absolute best to get going with the project as soon as possible. There are few better feelings for clients than seeing early progress on a project shortly after they’ve made that first payment.</p>
<p>We will check in with you and the client regularly just to make sure things are on track.</p>
<p><a href='http://app.shopexperts.com/expert/project/" . $this->project->id . "'>[Go to Project]</a></p>
<p>Good luck!</p>
<p>Shopexperts Team</p>",
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
