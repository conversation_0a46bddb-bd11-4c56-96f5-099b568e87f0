server {
    listen 8000;

    root /var/www/public;
    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    location / {
        try_files   $uri $uri/ /index.php$is_args$args;

        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Allow-Headers Range always;
        add_header Access-Control-Expose-Headers Content-Length always;
    }

    location ~ \.php {
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass   api:9000;
        fastcgi_index  index.php;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        include        fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
