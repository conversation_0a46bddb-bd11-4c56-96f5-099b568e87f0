{"name": "typescript", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint ."}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "@vueuse/core": "^13.3.0", "lucide-vue-next": "^0.503.0", "pinia": "^3.0.2", "vite-svg-loader": "^5.1.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.26.0", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.26.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.32.0", "vite": "^6.3.1", "vite-plugin-svg-loader": "^1.0.0", "vue-tsc": "^2.2.8"}}