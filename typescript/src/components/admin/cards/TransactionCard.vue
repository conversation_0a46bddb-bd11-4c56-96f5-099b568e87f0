<script setup lang="ts">
import type {ITransaction} from '../../../types.ts'
import Download from "../../../assets/icons/download.svg";

defineProps<{
  transaction: ITransaction
}>()
</script>

<template>
  <div class="bg-white rounded-md p-card-padding space-y-4 border border-grey">
    <div class="flex justify-between text-sm">
      <div class="flex flex-col text-h4">
        <span>Invoice #4921</span>
        <div class="flex items-center gap-1">
          <h3 class="font-medium">{{ transaction.type }}</h3>
          <span v-if="transaction.prepaidHours">({{ transaction.prepaidHours }})</span>
        </div>
      </div>
      <div class="flex flex-col text-h4">
        <span>Paid via</span>
        <h3 class="font-medium">{{ transaction.paymentMethod }}</h3>
      </div>
      <div class="flex flex-col text-h4">
        <span>Payment Date</span>
        <h3 class="font-medium">{{ transaction.paymentDate }}</h3>
      </div>
      <div class="flex flex-col text-h4">
        <span>Transaction Amount</span>
        <h3 class="font-medium">{{ transaction.transactionAmount }}</h3>
      </div>
    </div>

    <div class="flex justify-between border-t pt-4 items-center">
      <div class="grid grid-flow-col auto-cols-fr">
        <div v-if="transaction.expert" class="w-96 flex items-start space-x-3">
          <img
              :src="transaction.expert.avatar"
              alt="Expert avatar"
              class="w-[64px] h-[64px] rounded-full object-cover"
          />
          <div>
            <h4 class="text-tertiary">Expert</h4>
            <p class="text-primary font-medium">{{ transaction.expert.name }}</p>
            <h4 class="text-primary">Freelancer</h4>
            <a :href="`mailto:${transaction.expert.email}`" class="flex items-center gap-1 text-h4 text-link hover:underline">
              {{ transaction.expert.email }}
            </a>
          </div>
        </div>
        <div v-if="transaction.client" class="w-96 flex items-start space-x-3">
          <img
              :src="transaction.client.avatar"
              alt="Client avatar"
              class="w-[64px] h-[64px] rounded-full object-cover"
          />
          <div>
            <h4 class="text-tertiary">Client</h4>
            <p class="text-primary font-medium">{{ transaction.client.name }}</p>
            <a :href="`mailto:${transaction.client.email}`" class="flex items-center gap-1 text-h4 text-link hover:underline">
              {{ transaction.client.email }}
            </a>
            <h4 class="text-primary">Shopify Plan: {{ transaction.client.plan }}</h4>
          </div>
        </div>
      </div>

      <div class="flex justify-center items-center">
        <button
            class="flex justify-center items-start w-full px-3 py-1 bg-primary text-white rounded-sm gap-1.5"
        >
          <Download class="w-4 h-4" />
          <span class="text-h4">Download Invoice</span>
        </button>
      </div>
    </div>
  </div>
</template>
