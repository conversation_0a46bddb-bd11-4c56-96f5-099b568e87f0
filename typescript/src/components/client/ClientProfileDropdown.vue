<template>
  <div class="relative" ref="dropdownRef">
    <!-- Trigger -->
    <button
        @click="toggleDropdown"
        class="flex items-center border rounded-sm py-1 pl-3 pr-2 hover:bg-muted"
    >
      <span class="mr-2 hidden md:inline">My Profile</span>
      <img
          :src="profileImage"
          alt="Profile"
          class="w-8 h-8 rounded-full object-cover"
      />
    </button>

    <!-- Dropdown -->
    <div
        v-show="isOpen"
        class="absolute right-0 mt-2 w-[343px] bg-white border rounded-md shadow-lg z-50 p-4 flex flex-col items-start transition-opacity duration-200 ease-in-out"
    >
      <button class="w-full flex items-center px-4 py-2 text-left hover:bg-gray-100 gap-2">
        <SettingsIcon class="w-5 h-5" />
        Settings
      </button>
      <button class="w-full flex items-center px-4 py-2 text-left hover:bg-gray-100 gap-2">
        <TransactionsIcon class="w-5 h-5" />
        Transactions
      </button>
      <button class="w-full flex items-center px-4 py-2 text-left hover:bg-gray-100 gap-2">
        <TeamIcon class="w-5 h-5" />
        My Team
      </button>
      <button class="w-full flex items-center px-4 py-2 text-left hover:bg-gray-100 gap-2">
        <LogoutIcon class="w-5 h-5" />
        Logout
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onClickOutside } from '@vueuse/core'

import SettingsIcon from '../../assets/icons/settings.svg'
import TransactionsIcon from '../../assets/icons/transactions.svg'
import TeamIcon from '../../assets/icons/team.svg'
import LogoutIcon from '../../assets/icons/logout.svg'

defineProps<{ profileImage: string }>()

const isOpen = ref(false)
const dropdownRef = ref(null)

function toggleDropdown() {
  isOpen.value = !isOpen.value
}

// ✅ Close dropdown when clicking outside
onClickOutside(dropdownRef, () => {
  isOpen.value = false
})
</script>
