<template>
  <div class="space-y-14">
    <ExpertDirectMessageCard
        v-for="expert in directRequests"
        :key="expert.id"
        :expert="expert"
        class="flex flex-col gap-3"
    />

    <ExpertQuoteRequestCard
        v-for="quote in latestQuote"
        :key="quote.id"
        :quote="quote"
        class="flex flex-col gap-3"
    />

    <FeaturedExpertCard :experts="featuredExperts" />
    <FeaturedPackagedServices />
    <ShopifyProductUpdates :updates="productUpdates" />

  </div>
</template>

<script setup lang="ts">
import ExpertDirectMessageCard from "./cards/ExpertDirectMessageCard.vue";
import ExpertQuoteRequestCard from "./cards/ExpertQuoteRequestCard.vue";
import FeaturedExpertCard from "./cards/FeaturedExpertCard.vue";
import FeaturedPackagedServices from "./cards/FeaturedPackagedServices.vue";
import ShopifyProductUpdates from "./cards/ShopifyProductUpdates.vue";

import type { IExpertDirectMessage, IExpertQuote, IFeaturedExpert } from "../../types.ts";

const directRequests: IExpertDirectMessage[] = [
  {
    id: 1,
    request_id: 1,
    request_type: "Direct Message",
    name: 'Michael Oswald',
    developerRank: 'Freelancer',
    role: 'Senior Shopify Developer',
    avatarUrl: 'https://randomuser.me/api/portraits/men/10.jpg',
    submittedDate: '17 Dec, 2025',
    initialStatus: 'In Progress',
    rating: "5.00",
    numberOfReviews: 16,
    pendingQuote: ""
  },
]

const latestQuote: IExpertQuote[] = [
  {
    id: 1,
    request_id: 1,
    request_type: "Quote Request",
    title: "Collection Page Changes & Updates on Product Page",
    expert: {
      id: 101,
      name: 'Lena Hoffmann',
      role: 'Shopify Theme Specialist',
      avatarUrl: 'https://randomuser.me/api/portraits/women/45.jpg',
      submittedDate: '26 May, 2025',
      initialStatus: 'In Progress',
      rating: "4.87",
      numberOfReviews: 58,
      pendingQuote: "Submitted",
      hourlyRate: 95,
      estimatedTime: "12 hours",
      deadline: "03 Jun, 2025",
      totalToPay: "$1140.00",
      quoteStatus: "Submitted"
    },
    additionalExperts: [
      {
        id: 102,
        name: 'Aaron Patel',
        role: 'Fullstack Shopify Developer',
        avatarUrl: 'https://randomuser.me/api/portraits/men/24.jpg',
        submittedDate: '26 May, 2025',
        initialStatus: 'In Progress',
        rating: "4.92",
        numberOfReviews: 41,
        pendingQuote: "Submitted",
        hourlyRate: 110,
        estimatedTime: "10 hours",
        deadline: "04 Jun, 2025",
        totalToPay: "$1100.00",
        quoteStatus: "Submitted"
      },
      {
        id: 103,
        name: 'Tina Nguyen',
        role: 'Shopify UX Designer',
        avatarUrl: 'https://randomuser.me/api/portraits/women/35.jpg',
        submittedDate: '26 May, 2025',
        initialStatus: 'In Progress',
        rating: "4.89",
        numberOfReviews: 34,
        pendingQuote: "Pending",
        quoteStatus: "Pending"
        // ⛔ No quote fields here
      },
      {
        id: 104,
        name: 'George Ivanov',
        role: 'Backend Shopify Expert',
        avatarUrl: 'https://randomuser.me/api/portraits/men/39.jpg',
        submittedDate: '26 May, 2025',
        initialStatus: 'In Progress',
        rating: "4.80",
        numberOfReviews: 67,
        pendingQuote: "Pending",
        quoteStatus: "Pending"
        // ⛔ No quote fields here either
      }
    ]
  }
];

const featuredExperts: IFeaturedExpert[] = [
  {
    id: 1,
    name: 'Michael Oswald',
    role: 'Senior Shopify Developer',
    avatarUrl: 'https://randomuser.me/api/portraits/men/7.jpg',
    submittedDate: '17 Dec, 2025',
    initialStatus: 'In Progress',
    rating: "5.00",
    numberOfReviews: 16,
    pendingQuote: "Submitted",
    startingPrice: "85",
    services: ['Store Build or Redesign', 'Theme Customization', 'Store Settings Configuration']
  },
  {
    id: 2,
    name: 'Michael Oswald',
    role: 'Senior Shopify Developer',
    avatarUrl: 'https://randomuser.me/api/portraits/men/6.jpg',
    submittedDate: '17 Dec, 2025',
    initialStatus: 'In Progress',
    rating: "5.00",
    numberOfReviews: 16,
    pendingQuote: "Submitted",
    startingPrice: "85",
    services: ['Frontend Development', 'API Integration']
  },
  {
    id: 3,
    name: 'Michael Oswald',
    role: 'Senior Shopify Developer',
    avatarUrl: 'https://randomuser.me/api/portraits/men/5.jpg',
    submittedDate: '17 Dec, 2025',
    initialStatus: 'In Progress',
    rating: "5.00",
    numberOfReviews: 16,
    pendingQuote: "Submitted",
    startingPrice: "85",
    services: ['UX Research', 'Wireframing', 'Figma Prototypes']
  },
]

const productUpdates = [
  {
    title: 'Shop available in French, German and Spanish',
    category: 'Shop',
    description: 'Shop app and Shop web are available in French, German, and Spanish.',
    date: '17 Dec, 2025',
  },
  {
    title: 'Logo & Background Media on Customer Display',
    category: 'POS',
    description:
        'Now you can customize the appearance of the Idle Screen on your buyer facing display by uploading a logo and background image via the Display Editor in your Shopify Admin.',
    date: '12 Dec, 2025',
  },
]
</script>
