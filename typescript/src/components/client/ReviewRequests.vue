<script setup lang="ts">

import ReviewCard from "../common/cards/ReviewCard.vue";
import {ref} from "vue";

const reviews = ref([
  {
    reviewer: {
      id: 1,
      displayUrl: "https://randomuser.me/api/portraits/men/79.jpg",
      name: "<PERSON>",
      storeTitle: "SuperSport",
      recurringClient: true,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "Working with Lau<PERSON><PERSON> was a game-changer for our mobile store experience. The redesigned product grid is stunning and works flawlessly. Our mobile conversions have increased by 35%, thanks to his expertise!",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: true,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Organic",
    responses: [ "Hey <PERSON><PERSON>, hope you're doing well! I wanted to kindly ask if you could leave a review for the recent projects we worked on together. <br/>I'm just starting to build my profile on this platform, so a review from you would mean a lot. Thanks so much in advance!" ],
    status: 'Pending Review',
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      recurringExpert: true,
      isShopexpertUser: true,
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/men/19.jpg",
      rank: "Shopify Developer",
      type: "Freelancer",
    }
  },
  {
    reviewer: {
      id: 2,
      displayUrl: "https://randomuser.me/api/portraits/women/71.jpg",
      name: "Olivia R.",
      storeTitle: "Archery",
      recurringClient: false,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "Lautaro built an advanced filtering system that completely transformed how our customers shop. It’s fast, intuitive, and beautifully integrated into our site. His attention to detail is unmatched.",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: false,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Invited",
    responses: [ "Hey Filip, hope you're doing well! I wanted to kindly ask if you could leave a review for the recent projects we worked on together. <br/>I'm just starting to build my profile on this platform, so a review from you would mean a lot. Thanks so much in advance!" ],
    status: 'Approved',
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      recurringExpert: true,
      isShopexpertUser: true,
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/women/11.jpg",
      rank: "Shopify Developer",
      type: "Freelancer",
    }
  },
  {
    reviewer: {
      id: 3,
      displayUrl: "https://randomuser.me/api/portraits/men/76.jpg",
      name: "Jack R.",
      storeTitle: "United by Blue",
      recurringClient: false,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "We wanted a feature that let customers preview products in real-time, and Lautaro delivered beyond our expectations. The extension is seamless and our customers love it. He’s a Shopify wizard!",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: true,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Invited",
    responses: [ "Hey Filip, hope you're doing well! I wanted to kindly ask if you could leave a review for the recent projects we worked on together. <br/>I'm just starting to build my profile on this platform, so a review from you would mean a lot. Thanks so much in advance!" ],
    status: 'Approved',
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      recurringExpert: true,
      isShopexpertUser: true,
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/men/16.jpg",
      rank: "Shopify Developer",
      type: "Freelancer",
    }
  },
]);
</script>

<template>
  <main>
    <ReviewCard
        v-for="(review, index) in reviews"
        :key="index"
        :review="review"
        isClient
        isReviewRequest
    />
  </main>
</template>

<style scoped>

</style>