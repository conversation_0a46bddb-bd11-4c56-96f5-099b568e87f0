<template>
  <div class="mb-4 w-full">
    <div class="flex justify-between items-center flex-wrap gap-6">
      <!-- LEFT: Avatar + Info -->
      <div class="flex gap-4 items-center">
        <img :src="expert.avatarUrl" alt="Avatar" class="w-16 h-16 rounded-full" />
        <div class="flex flex-col">
          <div class="flex items-center gap-2">
            <p class="text-primary font-medium text-h4">{{ expert.name }}</p>
            <span
                v-if="expert.pendingQuote"
                class="text-xs font-medium px-2 py-0.5 rounded-sm"
                :class="{
                'text-pending bg-pending-light': expert.pendingQuote === 'Pending',
                'text-success bg-success-light': expert.pendingQuote === 'Active',
                'text-linkDarkBlue bg-linkBlue': expert.pendingQuote === 'Submitted'
              }"
            >
              {{ formatQuoteStatus(expert.pendingQuote) }}
            </span>
          </div>
          <p class="text-gray-700 text-sm">Freelancer</p>
          <p class="text-primary text-h4 font-normal">{{ expert.role }}</p>
          <div class="flex items-center gap-1 text-h5">
            <Star />
            <p class="font-semibold">
              {{ expert.rating }}
              <span class="text-tertiary font-normal"> ({{ expert.numberOfReviews }} reviews)</span>
            </p>
          </div>
        </div>
      </div>

      <!-- RIGHT: Group = Grid + Actions -->
      <div
          class="flex items-center flex-wrap justify-end lg:gap-6">
        <!-- Quote Meta Grid -->
        <div
            v-if="expert.quoteStatus === 'Submitted'"
            class="grid grid-cols-4 text-h5 text-gray-800 gap-14"
        >
          <div class="flex flex-col gap-1">
            <h5 class="text-primary font-normal">Hourly rate</h5>
            <p class="text-primary font-normal">${{ expert?.hourlyRate?.toFixed(2) }}</p>
          </div>
          <div class="flex flex-col gap-1">
            <h5 class="text-primary font-normal">Estimated time</h5>
            <p class="text-primary font-normal">{{ expert?.estimatedTime }}</p>
          </div>
          <div class="flex flex-col gap-1">
            <h5 class="text-primary font-normal">Deadline</h5>
            <p class="text-primary font-normal">{{ expert?.deadline }}</p>
          </div>
          <div class="flex flex-col gap-1">
            <h5 class="text-primary font-normal">Total to pay</h5>
            <p class="text-primary font-normal">{{ expert?.totalToPay }}</p>
          </div>
        </div>

        <!-- Action Buttons (Always Shown) -->
        <div class="flex gap-2 items-center min-w-[180px] justify-end">
          <router-link
              :to="`/expert/lead/${1}/chatroom`"
              class="px-4 py-2 border rounded text-h4 flex items-center gap-2"
              :class="{
                'text-black bg-white hover:bg-gray-100': expert.pendingQuote === 'Submitted',
                'text-white bg-primary hover:bg-gray-800': expert.pendingQuote !== 'Submitted',
              }"
          >
            <Chat v-if="expert.pendingQuote !== 'Submitted'" />
            <ChatBlack v-else />
            <span>Chat Now</span>
          </router-link>

          <router-link
              v-if="expert.pendingQuote === 'Submitted'"
              :to="`/expert/lead/${1}/chatroom`"
              class="px-4 py-2 rounded text-h4 flex items-center gap-2 text-white bg-primary hover:bg-gray-800"
          >
            <span>Accept Quote</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Chat from '../../../assets/icons/chat.svg'
import ChatBlack from '../../../assets/icons/chatBlack.svg'
import Star from '../../../assets/icons/star.svg'
import type { IExpertQuoteItem } from '../../../types.ts'

defineProps<{
  expert: IExpertQuoteItem
}>()

/**
 * Helper to map internal status to display labels
 */
function formatQuoteStatus(status: string): string {
  if (status === 'Submitted') return 'Quote Submitted'
  if (status === 'Pending') return 'Pending Quote'
  return status
}
</script>
