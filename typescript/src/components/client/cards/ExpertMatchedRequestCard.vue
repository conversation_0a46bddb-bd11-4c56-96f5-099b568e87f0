<template>
  <router-link
      :to="{
        name: 'RequestDetails',
        params: {
          requestId: expert.request_id,
          type: expert.request_type
        }
      }"
      class="block border border-grey rounded-md bg-white p-4 mb-4 hover:shadow-md transition"
  >
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center bg-lightApricot text-orangeBrown rounded-sm px-1">
        <p class="font-semibold text-custom1">Matched</p>
      </div>
      <h5 class="text-tertiary font-normal">Submitted on {{ expert.submittedDate }}</h5>
    </div>

    <h3 class="mb-4 font-semibold">{{ expert.title }}</h3>

    <div>
      <div class="flex justify-between gap-4">
        <!-- Left Side -->
        <div class="flex items-start gap-4">
          <img :src="expert.avatarUrl" alt="Avatar" class="w-16 h-16 rounded-full" />

          <div class="flex flex-wrap items-center gap-4 text-h4">
            <div class="flex flex-col">
              <div class="flex items-center gap-2">
                <p class="text-primary font-medium">{{ expert.name }}</p>
                <h5
                    v-if="expert.pendingQuote"
                    class="font-medium px-2 py-0.5 rounded-sm"
                    :class="{
                    'text-pending bg-pending-light': expert.pendingQuote === 'Pending',
                    'text-success bg-success-light': expert.pendingQuote === 'Active',
                    'text-link bg-link-light': expert.pendingQuote === 'Submitted'
                  }"
                >
                  {{ expert.pendingQuote }}
                </h5>
              </div>
              <h4 class="text-primary font-normal">{{ expert.developerRank }}</h4>
              <h4 class="font-normal text-primary">{{ expert.role }}</h4>
              <div class="flex items-center gap-2">
                <Star />
                <p class="font-semibold">
                  {{ expert.rating }}
                  <span class="text-tertiary font-normal"> ({{ expert.numberOfReviews }} reviews) </span>
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side Buttons -->
        <div class="flex items-center justify-center gap-2 mt-4">
          <router-link
              :to="`/expert/lead/${expert.id}/chatroom`"
              @click.stop
              class="px-4 py-2 border rounded text-h4 flex items-center gap-2"
              :class="{
              'text-black bg-white hover:bg-gray-100': expert.pendingQuote === 'Submitted',
              'text-white bg-primary hover:bg-gray-800': expert.pendingQuote !== 'Submitted',
            }"
          >
            <Chat v-if="expert.pendingQuote !== 'Submitted'" />
            <ChatBlack v-else />
            <span>Chat Now</span>
          </router-link>

          <router-link
              v-if="expert.pendingQuote === 'Submitted'"
              :to="`/expert/lead/${expert.id}/chatroom`"
              @click.stop
              class="px-4 py-2 rounded text-h4 flex items-center gap-2 text-white bg-primary hover:bg-gray-800"
          >
            <span>Accept Quote</span>
          </router-link>
        </div>
      </div>
    </div>
  </router-link>
</template>

<script setup lang="ts">
import Chat from '../../../assets/icons/chat.svg';
import ChatBlack from '../../../assets/icons/chatBlack.svg';
import Star from '../../../assets/icons/star.svg'
import type { IExpertMatched } from "../../../types.ts";

defineProps<{
  expert: IExpertMatched
}>()

// const status = ref(props.expert.initialStatus || 'In Progress')

</script>