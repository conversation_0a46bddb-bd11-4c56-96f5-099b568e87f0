<template>
  <router-link
      :to="{
        name: 'RequestDetails',
        params: {
          requestId: quote.request_id,
          type: quote.request_type
        }
      }"
      class="block border rounded-md shadow-sm bg-white mb-4 p-card-padding hover:shadow-md transition"
  >
    <div class="flex items-center justify-between mb-1">
      <div class="flex items-center bg-linkBlue text-[#176593] rounded-sm px-1">
        <p class="font-semibold text-custom1 ">
          Quote request
        </p>
      </div>
      <h5 class="text-gray-500 font-normal">Submitted on {{ quote.expert.submittedDate }}</h5>
    </div>

    <h3 class="mb-4 font-semibold">
      {{ quote.title }}
    </h3>

    <div>
      <ExpertCard
          :expert="quote.expert"
          class="flex flex-col gap-3"
      />
    </div>

    <div v-if="quote.additionalExperts">
      <p class="text-h6 tracking-widest text- font-normal">
        {{ quote.additionalExperts?.length || 0 }} ADDITIONAL EXPERTS
      </p>
      <div class="border-t w-full mb-1"></div>

      <div>
        <ExpertCard
            v-for="expert in quote.additionalExperts"
            :key="expert.id"
            :expert="expert"
            class="flex flex-col gap-3"
        />
      </div>
    </div>
  </router-link>
</template>
<script setup lang="ts">
import ExpertCard from "./ExpertCard.vue";
import type { IExpertQuote } from "../../../types.ts";

defineProps<{ quote: IExpertQuote }>()
</script>