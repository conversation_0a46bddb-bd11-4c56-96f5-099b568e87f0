<!-- src/components/client/FeaturedExperts.vue -->
<template>
  <div class="flex flex-col gap-6">
    <div class="flex justify-between items-center">
      <h3 class="text-primary font-semibold">Featured Experts</h3>
      <a href="#" class="text-primary text-paragraph font-normal hover:underline">Check Experts Directory</a>
    </div>
    <div class="border rounded-md shadow-sm bg-white">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <div
            v-for="expert in experts"
            :key="expert.id"
            class="border rounded-md bg-white shadow-sm p-4 hover:shadow-md transition flex flex-col justify-between"
        >

          <div class="flex flex-col gap-4">
            <div class="flex justify-between">
              <img :src="expert.avatarUrl" alt="Avatar" class="w-16 h-16 rounded-full" />
              <div class="text-h4 text-right text-tertiary font-sm">
                <p>Starting price:</p>
                <p class="text-h2 text-primary font-semibold">${{ expert.startingPrice }}</p>
              </div>
            </div>
            <div class="flex flex-col gap-2">
              <h2 class="text-primary font-semibold">{{ expert.name }}</h2>
              <h4 class="font-normal text-primary ">{{ expert.role }}</h4>
              <div class="flex items-center gap-2">
                <star class="w-5 h-5"/>
                <p class="font-semibold">{{expert.rating}} <span class="text-tertiary font-normal"> ({{expert.numberOfReviews}} reviews) </span></p>
              </div>
            </div>
            <!-- Service Categories -->
            <div class="flex flex-col gap-2">
              <h6 class="text-primary font-semibold">Service categories:</h6>
              <div class="flex flex-wrap gap-2">
              <span
                  v-for="category in expert.services"
                  :key="category"
                  class="px-3 py-1 text-h4 rounded-full text-primary border"
              >
                {{ category }}
              </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Star from '../../../assets/icons/star.svg'
import type { IFeaturedExpert } from '../../../types.ts'

defineProps<{
  experts: IFeaturedExpert[]
}>()
</script>
