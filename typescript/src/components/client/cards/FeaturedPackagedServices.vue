<template>
  <div class="flex flex-col gap-6">
    <div class="flex justify-between items-center">
      <h3 class="text-primary font-semibold">Featured Packaged Services</h3>
      <a href="#" class="text-primary text-paragraph font-normal hover:underline">Check All Services</a>
    </div>

    <div class="bg-white grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      <PackagedServices :services="featuredServices" />
    </div>
  </div>
</template>

<script setup lang="ts">

import PackagedServices from "./PackagedServices.vue";

const featuredServices = [
  {
    id: 1,
    title: 'Progress bar to show amount left to qualify for "Free Shipping"',
    image: 'https://plus.unsplash.com/premium_photo-1677487978412-1e27f45f3e0a?q=80&w=1932&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    deliveryTime: '2-3 business days',
    price: 550
  },
  {
    id: 2,
    title: 'Sticky "Add to Cart" button on product page',
    image: 'https://plus.unsplash.com/premium_photo-1681398731280-a08c591dfc58?q=80&w=1934&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    deliveryTime: '3-4 business days',
    price: 950
  },
  {
    id: 3,
    title: 'Shopify Custom Checkout Fields & Additional Order Notes',
    image: 'https://images.unsplash.com/photo-1674027392851-7b34f21b07ee?q=80&w=1932&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    deliveryTime: '4-5 business days',
    price: 700
  }
]

</script>
