<template>
  <div class="bg-muted mt-8 rounded-md">
    <h2 class="text-h3 font-semibold mb-4">Shopify Product Updates</h2>

    <div v-for="(update, index) in updates" :key="index" class="bg-white border rounded-md p-4 mb-3">
      <div class="flex justify-between mb-1">
        <span class="text-paragraph font-semibold">{{ update.title }}</span>
        <span class="text-gray-500 text-h5 font-normal">{{ update.date }}</span>
      </div>
      <div class="text-h5 font-sm mb-1">{{ update.category }}</div>
      <p class="font-sm">{{ update.description }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  updates: {
    title: string
    category: string
    description: string
    date: string
  }[]
}>()
</script>
