<template>
  <StepPanel>
    <div class="flex flex-col gap-8">
      <div class="flex flex-col gap-4">
        <div class="flex justify-between items-center">
          <p class="text-h4 font-normal font-archivo text-primary tracking-10p">
            FREE PROJECT QUOTE
          </p>
          <p class="text-h4 font-normal font-archivo text-primary tracking-10p">3/3</p>
        </div>
        <h1 class="font-normal text-primary">You are almost done!</h1>
        <p class="text-primary">
          We just need a couple more details and you're all set!
        </p>
      </div>

      <div class="flex flex-col gap-8">
        <BaseInput label="Your Email Address" v-model="formData.email" type="email" />

        <div class="flex gap-8">
          <BaseInput label="First Name" v-model="formData.firstName" class="w-1/2" />
          <BaseInput label="Last Name" v-model="formData.lastName" class="w-1/2" />
        </div>

        <ShopifyTierDropdown v-model="formData.shopifyPlan" />

        <div class="flex flex-col gap-1">
          <label class="text-h5 font-normal font-archivo text-primary">Password</label>
          <PasswordInput v-model="formData.password" />
        </div>

        <div class="flex flex-col gap-1">
          <label class="block text-h5 font-normal font-archivo text-primary">Confirm Password</label>
          <PasswordInput v-model="formData.confirmPassword" />
        </div>

        <div class="flex justify-between gap-8">
          <BaseButton :isPrimary="false" @click="goBack">Back</BaseButton>
          <BaseButton :isPrimary="true" @click="submitForm">Submit</BaseButton>
        </div>
      </div>
    </div>
  </StepPanel>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseInput from '../../../components/common/InputFields/BaseInput.vue'
import BaseButton from '../../../components/common/InputFields/BaseButton.vue'
import PasswordInput from '../../../components/common/InputFields/PasswordInput.vue'
import ShopifyTierDropdown from '../../../components/common/InputFields/ShopifyTierDropdown.vue'
import StepPanel from './StepPanel.vue'

const router = useRouter()
const formData = ref({
  email: '',
  firstName: '',
  lastName: '',
  shopifyPlan: '',
  password: '',
  confirmPassword: ''
})

onMounted(() => {
  // Load any previously saved data
  const saved = localStorage.getItem('accountInfo')
  if (saved) {
    formData.value = JSON.parse(saved)
  }
})

const submitForm = async () => {
  try {
    // Combine all form data from different steps
    const quotePreferences = JSON.parse(localStorage.getItem('quotePreferences') || '{}')
    const storeDetails = JSON.parse(localStorage.getItem('storeDetails') || '{}')
    const projectBrief = JSON.parse(localStorage.getItem('projectBrief') || '{}')

    const completeFormData = {
      ...quotePreferences,
      ...storeDetails,
      ...projectBrief,
      ...formData.value
    }

    // Here you would typically send the data to your backend
    console.log('Submitting form data:', completeFormData)

    // Clear localStorage after submission
    localStorage.removeItem('quotePreferences')
    localStorage.removeItem('storeDetails')
    localStorage.removeItem('projectBrief')
    localStorage.removeItem('accountInfo')

  } catch (error) {
    console.error('Error submitting form:', error)
  }
}

const goBack = () => {
  localStorage.setItem('accountInfo', JSON.stringify(formData.value))
  router.push('/client/free-quote/project-brief')
}
</script>