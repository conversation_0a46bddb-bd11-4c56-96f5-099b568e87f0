<template>
  <StepPanel>
    <div class="flex flex-col gap-8">
      <div class="flex flex-col gap-4">
        <p class="text-h4 font-normal font-archivo text-primary tracking-10p">
          FREE PROJECT QUOTE
        </p>
        <h1 class="font-normal text-primary">
          Receive a <i>free project quote</i> for your Shopify project
        </h1>
        <p class="text-primary">
          Describe your project or the challenge you're facing to receive a free project quote from one or more experts.
        </p>
      </div>

      <div class="flex flex-col gap-8">
        <div class="flex flex-col gap-1">
          <p class="text-h5 text-tertiary-dark font-normal">Preferred Expert</p>
          <div class="border border-lightGray rounded-md p-4 flex items-center gap-4">
            <img src="../../../assets/icons/prefferedExpert.png" class="w-12 h-12 rounded-full object-cover" />
            <div>
              <p class="font-archivo font-semibold"><PERSON></p>
              <p class="text-h4 text-primary font-normal">Shopify Developer</p>
            </div>
          </div>
        </div>

        <div class="flex items-start gap-4">
          <input
              type="checkbox"
              v-model="formData.sendToMoreExperts"
              class="translate-y-[2px] rounded-[4px] border border-lightGray accent-primary hover:accent-primary cursor-pointer"
          />
          <div>
            <p class="text-h4 text-primary font-semibold">
              Send this quote request to 3 additional experts
            </p>
            <p class="text-h5 text-primary font-normal">
              We'll automatically forward your quote request to three additional experts who match your criteria, so you can compare multiple quotes.
            </p>
          </div>
        </div>

        <div class="flex items-start gap-4">
          <input
              type="checkbox"
              v-model="formData.isUrgent"
              class="translate-y-[2px] rounded-[4px] border border-lightGray accent-primary hover:accent-primary cursor-pointer"
          />
          <div>
            <p class="text-h4 text-primary font-semibold">This project is urgent</p>
            <p class="text-h5 text-primary font-normal">
              If you need a fast turnaround, let experts know your project is urgent.
            </p>
          </div>
        </div>

        <div class="flex flex-col gap-2">
          <BaseButton :isPrimary="true" @click="proceed">Continue</BaseButton>
          <BaseButton :isPrimary="false" @click="goBackToWebsite">Back to Website</BaseButton>
        </div>
      </div>
    </div>
  </StepPanel>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import StepPanel from './StepPanel.vue'
import BaseButton from '../../../components/common/InputFields/BaseButton.vue'


const router = useRouter()
const formData = ref({
  sendToMoreExperts: false,
  isUrgent: false
})

const proceed = () => {
  localStorage.setItem('quotePreferences', JSON.stringify(formData.value))
  router.push('/client/free-quote/agency-details')
}

const goBackToWebsite = () => {
  window.location.href = '/'
}
</script>