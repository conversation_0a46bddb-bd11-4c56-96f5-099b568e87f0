<template>
  <StepPanel>
    <div class="flex flex-col gap-8">
      <div class="flex flex-col gap-4">
        <div class="flex justify-between items-center">
          <p class="text-h4 font-normal font-archivo text-primary tracking-10p">
            FREE PROJECT QUOTE
          </p>
          <p class="text-h4 font-normal font-archivo text-primary tracking-10p">2/3</p>
        </div>
        <h1 class="font-normal text-primary">
          Receive a <i>free project quote</i> for your Shopify project
        </h1>
        <p class="text-primary">
          Describe your project or the challenge you're facing to receive a free project quote from one or more experts.
        </p>
      </div>

      <div class="flex flex-col gap-8">
        <div class="flex flex-col gap-1">
          <label class="block text-h5 font-normal font-archivo text-primary">Project Brief</label>
          <div class="rounded-md border border-lightGray overflow-hidden">
            <BaseInput
                v-model="formData.projectBrief"
                textarea
                :rows="6"
                noStyle
                hideLabel
            />

            <div class="border-t border-lightGray">
              <button
                  class="w-full px-4 py-2 text-tertiary-dark font-normal flex items-center gap-2"
              >
                <img :src="attachFileIcon" class="w-5 h-5" />
                Attach files
              </button>
            </div>
          </div>
        </div>

        <div class="flex justify-between gap-8">
          <BaseButton :isPrimary="false" @click="goBack">Back</BaseButton>
          <BaseButton :isPrimary="true" @click="proceed">Next</BaseButton>
        </div>
      </div>
    </div>
  </StepPanel>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseInput from '../../../components/common/InputFields/BaseInput.vue'
import BaseButton from '../../../components/common/InputFields/BaseButton.vue'
import StepPanel from './StepPanel.vue'

const router = useRouter()
const formData = ref({
  projectBrief: ''
})

const attachFileIcon = new URL('../../../assets/icons/attachFile.svg', import.meta.url).href

onMounted(() => {
  // Load saved data if available
  const saved = localStorage.getItem('projectBrief')
  if (saved) {
    formData.value = JSON.parse(saved)
  }
})

const proceed = () => {
  // Save to localStorage before proceeding
  localStorage.setItem('projectBrief', JSON.stringify(formData.value))
  router.push('/client/free-quote/account-info')
}

const goBack = () => {
  router.push('/client/free-quote/agency-details')
}
</script>