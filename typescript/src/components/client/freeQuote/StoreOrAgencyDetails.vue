<template>
  <StepPanel>
    <div class="flex flex-col gap-8">
      <div class="flex flex-col gap-4">
        <div class="flex justify-between items-center">
          <p class="text-h4 font-normal font-archivo text-primary tracking-10p">
            FREE PROJECT QUOTE
          </p>
          <p class="text-h4 font-normal font-archivo text-primary tracking-10p">1/3</p>
        </div>
        <h1 class="font-normal text-primary">
          Receive a <i>free project quote</i> for your Shopify project
        </h1>
        <p class="text-primary">
          Describe your project or the challenge you're facing to receive a free project quote from one or more experts.
        </p>
      </div>

      <div class="flex flex-col gap-8">
        <BaseInput label="Shopify Store or Agency Name" v-model="formData.storeName" />
        <BaseInput label="Shopify Store URL or Agency Website" v-model="formData.storeUrl" />
        <BaseInput label="Project Title" v-model="formData.projectTitle" />

        <div class="flex justify-between gap-8">
          <BaseButton :isPrimary="false" @click="goBack">Back</BaseButton>
          <BaseButton :isPrimary="true" @click="proceed">Next</BaseButton>
        </div>
      </div>
    </div>
  </StepPanel>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseInput from '../../../components/common/InputFields/BaseInput.vue'
import BaseButton from '../../../components/common/InputFields/BaseButton.vue'
import StepPanel from './StepPanel.vue'

const router = useRouter()
const formData = ref({
  storeName: '',
  storeUrl: '',
  projectTitle: ''
})

onMounted(() => {
  const saved = localStorage.getItem('storeDetails')
  if (saved) formData.value = JSON.parse(saved)
})

const proceed = () => {
  localStorage.setItem('storeDetails', JSON.stringify(formData.value))
  router.push('/client/free-quote/project-brief')
}

const goBack = () => {
  router.push('/client/free-quote')
}
</script>