<template>
  <StepPanel>
    <div class="flex flex-col gap-8">
      <div class="flex flex-col gap-4">
        <div class="flex justify-between items-center">
          <h4 class="font-normal font-archivo text-primary tracking-10p">
            Get Matched
          </h4>
          <h4 class="font-normal font-archivo text-primary">3/3</h4>
        </div>
        <h1 class="font-normal text-primary">
          We just need a few information so we can <i>confirm your identity.</i>
        </h1>
      </div>

      <div class="flex flex-col gap-8">
        <BaseInput label="Your Email Address" v-model="formData.email" type="email" />
        <div class="flex gap-8">
          <BaseInput label="First Name" v-model="formData.firstName" class="w-1/2" />
          <BaseInput label="Last Name" v-model="formData.lastName" class="w-1/2" />
        </div>
        <ShopifyTierDropdown v-model="formData.shopifyPlan" />
        <div class="flex flex-col gap-1">
          <label for="password" class="text-h5 font-normal font-archivo text-primary">Password</label>
          <PasswordInput v-model="formData.password" />
        </div>
        <div class="flex flex-col gap-1">
          <label for="password" class="block text-h5 font-normal font-archivo text-primary">Confirm Password</label>
          <PasswordInput v-model="formData.confirmPassword" />
        </div>

        <div class="flex justify-between gap-8">
          <BaseButton :isPrimary="false" @click="goBack">Back</BaseButton>
          <BaseButton :isPrimary="true" @click="submitForm">Submit</BaseButton>
        </div>
      </div>
    </div>
  </StepPanel>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseInput from '../../../components/common/InputFields/BaseInput.vue'
import BaseButton from '../../../components/common/InputFields/BaseButton.vue'
import ShopifyTierDropdown from "../../../components/common/InputFields/ShopifyTierDropdown.vue"
import PasswordInput from "../../../components/common/InputFields/PasswordInput.vue"
import StepPanel from './StepPanel.vue'

const router = useRouter()
const formData = ref({
  email: '',
  firstName: '',
  lastName: '',
  shopifyPlan: '',
  password: '',
  confirmPassword: ''
})

onMounted(() => {
  const saved = localStorage.getItem('matchAccount')
  if (saved) formData.value = JSON.parse(saved)
})

const submitForm = () => {
  const completeData = {
    ...JSON.parse(localStorage.getItem('matchDetails') || '{}'),
    ...JSON.parse(localStorage.getItem('matchBrief') || '{}'),
    ...formData.value
  }

  console.log('Submitting matched data:', completeData)

  // Clear storage
  localStorage.removeItem('matchDetails')
  localStorage.removeItem('matchBrief')
  localStorage.removeItem('matchAccount')

  router.push('/match-submitted')
}

const goBack = () => {
  localStorage.setItem('matchAccount', JSON.stringify(formData.value))
  router.push('/client/get-matched/project-brief')
}
</script>