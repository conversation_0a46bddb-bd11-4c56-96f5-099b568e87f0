<template>
  <button
      :class="[
      'px-4 py-2 text-sm font-semibold rounded-md border',
      isPrimary
        ? 'bg-black text-white hover:bg-accent hover:text-black'
        : 'bg-white text-black border-tertiary hover:bg-white hover:border-primary',
    ]"
      @click="$emit('click')"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
defineProps({
  isPrimary: {
    type: Boolean,
    default: false
  }
})
defineEmits(['click'])
</script>
