<script setup lang="ts">
import {ref} from "vue";
import type {IInvoice} from "../../types.ts";
import InvoiceCard from "../expert/cards/InvoiceCard.vue";

const invoices = ref<IInvoice[]>([
  {
    type: 'Expert Premium Subscription',
    paymentMethod: 'Direct Payment',
    paymentDate: '10 Dec, 2025',
    transactionAmount: '$950.00',
    isStandardRate: false,
  },
  {
    type: 'Project Quote',
    paymentMethod: 'Direct Payment',
    paymentDate: '10 Dec, 2025',
    transactionAmount: '$950.00',
    isStandardRate: true,
  },
  {
    type: 'Add to Scope',
    paymentMethod: 'Direct Payment',
    paymentDate: '10 Dec, 2025',
    transactionAmount: '$950.00',
    isStandardRate: true,
  },
  {
    type: 'Prepaid Hours',
    paymentMethod: 'Direct Payment',
    paymentDate: '10 Dec, 2025',
    transactionAmount: '$950.00',
    isStandardRate: false,
  },
])
</script>

<template>
  <div class="space-y-4">
    <InvoiceCard v-for="(invoice, index) in invoices" :key="index" :invoice="invoice" />
  </div>
</template>

