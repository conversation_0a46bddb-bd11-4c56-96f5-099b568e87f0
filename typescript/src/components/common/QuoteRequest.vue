<script setup lang="ts">
import {ref, defineProps} from "vue";
import type { ICollectionQuote } from "../../types.ts";
import QuoteCard from "./cards/QuoteCard.vue";

const props = defineProps<{
  isClientSide?: boolean
}>();

const projectDesc = ref<string>("We'd like to make some changes to the presentation of collection pages:<br/><br/>1.) Remove the background Image at the top of collection pages.<br/>2.) Change the heading color to black.<br/>3.) Center the heading.<br/>4.) Remove the breadcrumb trail.<br/>5.) Remove the sort and filter dropdowns.<br/><br/>We would also like to round the corners (5px border radius) of the 'See all X+' image (see attachment) and also break up the text to two lines, with 'See All' on the first line and 'X+' on the second line.")
const quotes = ref<ICollectionQuote[]>([
  {
    id: 1,
    type: "Quote",
    hourlyRate: 125.00,
    status: "Paid",
    estimatedTime: "10 hours",
    deadline: "27 Dec, 2025",
    totalPayment: "$1250.00",
    createdAt: "17 Dec, 2025 / 09:45am"
  },
  {
    id: 2,
    type: "Scope",
    hourlyRate: 125.00,
    status: "Pending",
    estimatedTime: "2 hours",
    deadline: "27 Dec, 2025",
    totalPayment: "$250.00",
    createdAt: "17 Dec, 2025 / 09:45am"
  }
]);
</script>

<template>
  <div class="p-6 border border-grey bg-white rounded-md space-y-6">
    <!-- If client side: show quotes first -->
    <template v-if="props.isClientSide">
      <div class="flex flex-col gap-2">
        <h4 class="font-semibold">Experts Offers</h4>
        <QuoteCard
            v-for="quote in quotes"
            :key="quote.id"
            :quote="quote"
            :is-client-side="props.isClientSide"
        />
      </div>

      <div class="space-y-4">
        <h4 class="pb-2 border-b border-grey font-semibold">
          Quote Request Description
        </h4>
        <p v-html="projectDesc"></p>
      </div>
    </template>

    <!-- Else show description first -->
    <template v-else>
      <div class="space-y-4">
        <h4 class="pb-2 border-b border-grey font-semibold">
          Quote Request Description
        </h4>
        <p v-html="projectDesc"></p>
      </div>

      <div class="space-y-2">
        <h4 class="font-semibold">Your Quotes</h4>
        <QuoteCard v-for="quote in quotes" :key="quote.id" :quote="quote" />
      </div>
    </template>
  </div>
</template>
