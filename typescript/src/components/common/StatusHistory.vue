<script setup lang="ts">
import {ref} from "vue";
import type {IStatusHistory} from "../../types.ts";
import StatusHistoryCard from "../expert/cards/StatusHistoryCard.vue";

const statusHistory = ref<IStatusHistory[]>([
  {
    description: '<PERSON> submitted the quote request.',
    time: '21 Feb, 2025 / 02:59pm',
  },
  {
    description: '<PERSON> sent a custom quote.',
    time: '21 Feb, 2025 / 02:59pm',
  },
  {
    description: '<PERSON> accepted and paid a quote.',
    time: '21 Feb, 2025 / 02:59pm',
  },
  {
    description: '<PERSON> marked the project as Completed.',
    time: '21 Feb, 2025 / 02:59pm',
  },
  {
    description: '<PERSON> market the project as Completed.',
    time: '21 Feb, 2025 / 02:59pm',
  },
])
</script>

<template>
  <div class="space-y-4">
    <StatusHistoryCard v-for="(history, index) in statusHistory" :key="index" :statusHistory="history" />
  </div>
</template>

