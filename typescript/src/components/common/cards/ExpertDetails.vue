<script setup lang="ts">
import Star from '../../../assets/icons/star.svg'
import { defineProps } from 'vue'
const props = defineProps<{ type: 'Quote Request' | 'Matched' | 'Direct Message' }>()

const currentSelectedExpert = {
  name: '<PERSON>',
  type: 'Freelancer',
  role: 'Senior Shopify Developer',
  avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
  rating: '4.95',
  numberOfReviews: 32,
  quoteType: props.type,
  budget: 2500,
  submittedDate: '18 May, 2025',
  location: 'United Kingdom',
  localTime: 'Wed, 03:56pm',
  avgResponse: '36 minutes',
}
</script>

<template>
  <aside class="bg-white border border-gray-200 p-4 flex flex-col gap-4 rounded-lg w-full max-w-xs shadow-sm self-start">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <h4 class="font-semibold text-primary">Expert Details</h4>
      <svg class="w-4 h-4 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
        <path d="M19 9l-7 7-7-7" />
      </svg>
    </div>

    <!-- Avatar + Info -->
    <div class="flex flex-col gap-2 items-start text-center">
      <img :src="currentSelectedExpert.avatar" alt="avatar" class="w-16 h-16 rounded-full object-cover" />
      <p class="font-semibold text-primary">{{ currentSelectedExpert.name }}</p>
      <h4 class="text-primary font-normal">{{ currentSelectedExpert.type }}</h4>
      <h4 class="text-primary font-normal">{{ currentSelectedExpert.role }}</h4>
      <div class="flex items-center gap-2">
        <Star class="w-4 h-4" />
        <div class="flex gap-1">
          <span class="text-paragraph font-semibold text-primary">{{ currentSelectedExpert.rating }}</span>
          <span class="text-paragraph font-normal text-slateGray">({{ currentSelectedExpert.numberOfReviews }} reviews)</span>
        </div>
      </div>
    </div>

    <!-- Info List -->
    <div class="flex flex-col gap-2">
      <div class="flex justify-between">
        <span class="text-tertiary text-h5">Type</span>
        <span
            class="text-custom1 font-semibold px-2 py-0.5 rounded"
            :class="{
            'bg-babyBlue text-deepBlue': currentSelectedExpert.quoteType === 'Quote Request',
            'bg-lightApricot text-earthyOrangeBrown': currentSelectedExpert.quoteType === 'Matched',
            'bg-lightPurple text-deepViolet': currentSelectedExpert.quoteType === 'Direct Message'
          }"
                >
          {{ currentSelectedExpert.quoteType }}
        </span>

      </div>
      <div class="flex justify-between">
        <span class="text-tertiary text-h5">Budget</span>
        <span class="font-normal text-h5 text-primary">${{ currentSelectedExpert.budget.toFixed(2) }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-tertiary text-h5">Request Submitted</span>
        <span class="font-normal text-h5 text-primary">{{ currentSelectedExpert.submittedDate }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-tertiary text-h5">Experts Location</span>
        <span class="font-normal text-h5 text-primary">{{ currentSelectedExpert.location }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-tertiary text-h5">Local Time</span>
        <span class="font-normal text-h5 text-primary">{{ currentSelectedExpert.localTime }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-tertiary text-h5">Avg. response time:</span>
        <span class="font-normal text-h5 text-primary">{{ currentSelectedExpert.avgResponse }}</span>
      </div>
    </div>

    <!-- CTA Button -->
    <button
        class="w-full bg-black text-white text-sm font-medium py-2 rounded hover:bg-gray-800 transition"
    >
      Open Expert Profile
    </button>
  </aside>
</template>
