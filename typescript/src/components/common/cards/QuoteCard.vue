<script setup lang="ts">
import type { ICollectionQuote } from "../../../types.ts";
import { computed } from "vue";

const props = defineProps<{
  quote: ICollectionQuote,
  isClientSide?: boolean
}>()

const statusStyle = computed(() => {
  switch (props.quote.status) {
    case 'Paid':
      return 'bg-softgreen text-success'
    case 'Rejected':
      return 'bg-softpink text-darkpink'
    case 'Pending':
    default:
      return 'bg-pending-light text-pending'
  }
})

const quoteLabel = computed(() => {
  if (props.quote.type === 'Quote') {
    return props.isClientSide ? 'Project Quote' : 'Your Quote'
  }
  return 'Add to Scope'
})

const showClientActions = computed(() => {
  return props.isClientSide && props.quote.status === 'Pending'
})
</script>

<template>
  <div class="rounded-sm bg-secondary border border-grey px-6 py-4 flex flex-col gap-4">
    <!-- Quote content row -->
    <div class="grid grid-flow-col auto-cols-fr items-center">
      <div>
        <div class="flex gap-1 items-center">
          <p class="font-semibold">{{ quoteLabel }}</p>
          <p :class="['rounded-sm px-2 py-1 font-semibold text-h5', statusStyle]">{{ quote.status }}</p>
        </div>
        <div class="text-h5 text-greyExtraDark">{{ quote.createdAt }}</div>
      </div>
      <div>
        <h5 class="font-normal">Hourly rate</h5>
        <p class="font-normal">${{ quote.hourlyRate }}</p>
      </div>
      <div>
        <h5 class="font-normal">Estimated time</h5>
        <p class="font-normal">{{ quote.estimatedTime }}</p>
      </div>
      <div>
        <h5 class="font-normal">Deadline</h5>
        <p class="font-normal">{{ quote.deadline }}</p>
      </div>
      <div class="text-right">
        <h5 class="font-normal">Total to pay</h5>
        <p class="font-normal">{{ quote.totalPayment }}</p>
      </div>
    </div>

    <!-- Conditional client action buttons -->
    <div v-if="showClientActions" class="flex justify-start gap-2">
      <button class="bg-primary text-white px-3 py-1 rounded-md font-semibold text-h4">Accept & Pay</button>
      <button class="bg-white text-black border border-gray px-3 py-1 rounded-md font-semibold">Decline</button>
    </div>
  </div>
</template>
