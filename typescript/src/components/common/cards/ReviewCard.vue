<script setup lang="ts">
import ExternalLink from "../../../assets/icons/externalLink.svg";
import Pencil from '../../../assets/icons/pencil.svg'
import Star from '../../../assets/icons/star.svg'
import ShopexpertMini from '../../../assets/icons/shopexpert-mini.svg'
import Recurring from '../../../assets/icons/recurring.svg'
import type {IReview} from "../../../types.ts";
import Info from "../../../assets/icons/info-sm.svg";

defineProps<{
  review: IReview
  isAdmin?: boolean
  isClient?: boolean
  isExpert?: boolean
  isReviewRequest?: boolean
}>()

</script>

<template>
  <div class="mx-auto bg-card border rounded-md shadow-sm p-card-padding space-y-4 mb-4">
    <div class="flex justify-between items-start">
      <div v-if="isAdmin || isExpert" class="flex items-center space-x-4">
        <img
            :src="review.reviewer.displayUrl"
            alt="Reviewer avatar"
            class="w-12 h-12 rounded-full object-cover"
        />
        <div>
          <div class="flex items-center space-x-2">
            <p class="font-normal">{{ review.reviewer.name }}</p>
            <Recurring v-if="review.reviewer.recurringClient" />
            <div
                v-if="review.reviewer.isShopexpertUser"
                class="inline-flex items-center gap-2 px-3 py-1 rounded-full border border-accent bg-accent-light text-sm font-medium text-gray-900"
            >
              <ShopexpertMini class="w-4 h-4" />
              Hired on shopexperts
            </div>
          </div>
          <a :href="review.reviewer.storeUrl" class="flex text-link text-h4 hover:underline items-center gap-1">
            {{ review.reviewer.storeTitle }}
            <ExternalLink />
          </a>
        </div>
      </div>
      <div v-else class="flex items-center space-x-4">
        <img
            :src="review.expert.displayUrl"
            alt="Expert avatar"
            class="w-12 h-12 rounded-full object-cover"
        />
        <div>
          <div class="flex items-center space-x-2">
            <p class="font-normal">{{ review.expert.name }}</p>
            <Recurring v-if="review.expert?.recurringExpert" />
            <div
                v-if="review.expert?.isShopexpertUser"
                class="flex items-center gap-1.5 px-3 py-1 rounded-full border border-accent bg-accent-light text-sm font-medium text-gray-900"
            >
              <ShopexpertMini class="w-5 h-5" />
              <span>Hired on shopexperts</span>
            </div>
          </div>
          <h4 class="text-primary">
            {{ review.expert?.rank }}
          </h4>
          <h4 class="text-primary">
            {{ review.expert?.type }}
          </h4>
        </div>
      </div>
      <div class="text-h5 font-light">
        <div class="justify-items-end space-y-2">
          <h5
              v-if="isAdmin"
              class="font-normal px-2 py-1 rounded-sm"
              :class="{
                'text-pending bg-pending-light': review.status === 'Pending Review',
                'text-success bg-success-light': review.status === 'Approved'
              }"
          >
            {{ review.status }}
          </h5>
          <h5>Requested: {{ review.postedAt }}</h5>
        </div>
      </div>
    </div>

    <!--  Body  -->
    <div v-if="isClient && isReviewRequest" class="space-y-6">
      <div v-for="response in review.responses">
        <div class="p-4 space-y-2 border border-grey rounded-md bg-greyLight">
          <div class="text-greyDark text-paragraph font-normal">Expert messages</div>
          <div class="text-primary text-paragraph font-normal" v-html="response"></div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="text-h4 flex items-center space-x-2 font-light mb-4">
        <span>Rating:</span>
        <span class="font-semibold">{{ review.reviewer.rating }}</span>
        <div class="flex space-x-1 text-accent">
          <template v-for="(_, i) in Array(5)" :key="i">
            <Star :index="i"/>
          </template>
        </div>
      </div>

      <p class="font-light leading-relaxed mb-4">
        {{ review.reviewer.review }}
      </p>

      <div class="text-h4 flex flex-wrap divide-x divide-grey font-light mb-6">
        <div class="pr-8">
          <span class="block text-h6 font-normal mb-1">Likely to recommend:</span>
          <h4 class="text-success font-medium">{{ review.reviewer.likelyToRecommend }}</h4>
        </div>
        <div class="px-8">
          <span class="block text-h6 font-normal mb-1">Project Value</span>
          <span class="font-medium text-h4">{{ review.projectValue }}</span>
        </div>
        <div class="pl-8">
          <div class="flex items-center gap-1 mb-1">
            <span class="block text-h6 font-normal">Review Source</span>
            <Info/>
          </div>
          <span class="font-medium text-h4">{{ review.reviewSource }}</span>
        </div>
      </div>
    </div>

    <!--  Footer  -->
    <div v-if="isAdmin" class="pt-6 border-t border-grey flex justify-between">
      <div class="flex items-start gap-2">
        <img
            :src="review.expert.displayUrl"
            alt="Reviewer avatar"
            class="w-12 h-12 rounded-full object-cover"
        />
        <div>
          <div class="text-h4 text-tertiary">Expert</div>
          <p class="text-primary font-medium">{{ review.expert.name }}</p>
          <a :href="review.expert.storeUrl" class="flex text-link text-h4 hover:underline items-center gap-1">
            {{ review.expert.storeTitle }}
            <ExternalLink />
          </a>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div v-if="review.status === 'Pending Review'" class="space-x-4">
          <button class="rounded-sm bg-primary text-h4 text-white border py-1 px-2">
            Approve Review
          </button>
          <button class="rounded-sm bg-white text-h4 text-primary border py-1 px-2">
            Decline Review
          </button>
        </div>
        <div v-else>
          <button class="rounded-sm bg-white text-h4 text-primary border py-1 px-2">
            Hide Review
          </button>
        </div>
        <button class="flex gap-2 text-h4 hover:underline">
          <Pencil />
          Edit Review
        </button>
      </div>
    </div>
    <div v-else-if="isClient" class="pt-4 border-t border-grey">
      <button class="flex items-center space-x-2 font-medium text-h4 hover:underline">
        <Pencil />
        <span>Write Your Review</span>
      </button>
    </div>
    <div v-else class="pt-4 border-t border-grey">
      <button class="flex items-center space-x-2 font-medium text-h4 hover:underline">
        <Pencil />
        <span>Write Your Response</span>
      </button>
    </div>
  </div>
</template>

<style scoped>

</style>
