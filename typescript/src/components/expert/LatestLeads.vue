<template>
  <div>
    <div class="mb-14">
      <LeadCard
          v-for="lead in latestLeads"
          :key="lead.id"
          :name="lead.name"
          :email="lead.email"
          :store-name="lead.storeName"
          :store-url="lead.storeUrl"
          :shopify-plan="lead.shopifyPlan"
          :avatar-url="lead.avatarUrl"
          :submitted-date="lead.submittedDate"
          :initial-status="lead.initialStatus"
          :budget="lead.budget"
          :type="lead.type"
          class="flex flex-col gap-3"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import LeadCard from './cards/LeadCard.vue'
// import LeaderboardColumn from './cards/LeaderboardColumnCard.vue'
// import {ref} from "vue";
// import type {ILeaderboard} from "../../types.ts";

// const range = ref('last_30_days')
const latestLeads = [
  {
    id: 1,
    name: '<PERSON>',
    email: 'micha<PERSON>@supersport.com',
    storeName: 'SuperStore',
    storeUrl: 'https://superstore.com',
    shopifyPlan: 'Advanced',
    avatarUrl: 'https://randomuser.me/api/portraits/men/10.jpg',
    submittedDate: '17 Dec, 2025',
    initialStatus: 'In Progress',
    budget: null,
    type: 'direct', // or 'quote'
  },
  {
    id: 2,
    name: 'Jack Rustowski',
    email: '<EMAIL>',
    storeName: 'KittyShop',
    storeUrl: 'https://kittyshop.com',
    shopifyPlan: 'Shopify',
    avatarUrl: 'https://randomuser.me/api/portraits/men/22.jpg',
    submittedDate: '17 Dec, 2025',
    initialStatus: 'In Progress',
    budget: '$2500.00',
    type: 'quote',
  },
  {
    id: 3,
    name: 'Paul Wilkinson',
    email: '<EMAIL>',
    storeName: 'CoffeeOasis',
    storeUrl: 'https://coffeeoasis.com',
    shopifyPlan: 'Advanced',
    avatarUrl: 'https://randomuser.me/api/portraits/men/30.jpg',
    submittedDate: '17 Dec, 2025',
    initialStatus: 'In Progress',
    budget: null,
    type: 'direct',
  },
  {
    id: 4,
    name: 'Jack Rancher',
    email: '<EMAIL>',
    storeName: 'United by Blue',
    storeUrl: 'https://unitedbyblue.com',
    shopifyPlan: 'Plus',
    avatarUrl: 'https://randomuser.me/api/portraits/men/40.jpg',
    submittedDate: '17 Dec, 2025',
    initialStatus: 'In Progress',
    budget: '$3500.00',
    type: 'quote',
  },
]

// const mostReviewsLeaders: ILeaderboard[] = [
//   { rank: 1, name: 'Kristin W.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/women/1.jpg' },
//   { rank: 2, name: 'Jenny W.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/women/2.jpg' },
//   { rank: 3, name: 'Savannah N.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/women/3.jpg' },
//   { rank: 4, name: 'Bessie C.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/men/24.jpg' },
//   { rank: 5, name: 'Cameron Z.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/men/88.jpg' }
// ]

// const highestRatedLeaders: ILeaderboard[] = [
//   { rank: 1, name: 'Richard B.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/women/1.jpg' },
//   { rank: 2, name: 'Eleanor P.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/men/2.jpg' },
//   { rank: 3, name: 'Arlen M.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/women/23.jpg' },
//   { rank: 4, name: 'Bessie C.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/men/24.jpg' },
//   { rank: 5, name: 'Daniel R.', role: 'Freelancer', rating: '4.95', reviews: 32, avatar: 'https://randomuser.me/api/portraits/men/18.jpg' }
// ]

// const responseTimeLeaders: ILeaderboard[] = [
//   { rank: 1, name: 'Mariah F.', role: 'Freelancer', responseTime: '32 minutes', avatar: 'https://randomuser.me/api/portraits/women/1.jpg' },
//   { rank: 2, name: 'Oswald R.', role: 'Freelancer', responseTime: '34 minutes', avatar: 'https://randomuser.me/api/portraits/men/2.jpg' },
//   { rank: 3, name: 'Theresa W.', role: 'Freelancer', responseTime: '40 minutes', avatar: 'https://randomuser.me/api/portraits/women/13.jpg' },
//   { rank: 4, name: 'Marvin M.', role: 'Freelancer', responseTime: '45 minutes', avatar: 'https://randomuser.me/api/portraits/men/22.jpg' },
//   { rank: 5, name: 'Olivia K.', role: 'Freelancer', responseTime: '47 minutes', avatar: 'https://randomuser.me/api/portraits/women/24.jpg' }
// ]

</script>
