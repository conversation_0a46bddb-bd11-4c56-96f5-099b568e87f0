<template>
  <StepPanel>
    <div class="flex flex-col gap-8">
      <div class="flex flex-col gap-4">
        <div class="flex flex-row items-center justify-between">
          <h4 class="font-normal font-archivo text-primary tracking-10p">
            {{ listingType === 'freelancer' ? 'CREATE FREELANCER LISTING' : 'CREATE AGENCY LISTING' }}
          </h4>
          <h4 class="font-normal font-archivo text-primary tracking-10p">1/3</h4>
        </div>
        <h1 class="font-normal text-primary">Contact Information</h1>
        <p class="text-primary">
          Let’s start with the information leads can use to contact you. <br>
          The email you enter here will be connected to your expert account.
        </p>
      </div>

      <div class="flex flex-col gap-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <BaseInput label="First Name" placeholder="First Name" v-model="formData.firstName" />
          <BaseInput label="Last Name" placeholder="Last Name" v-model="formData.lastName" />
        </div>

        <BaseInput label="Email" placeholder="Enter your email" v-model="formData.email" type="email" />
        <CountryDropdown v-model="formData.country" />

        <BaseInput
            v-if="listingType === 'freelancer'"
            label="Portfolio / Website URL (Optional)"
            v-model="formData.website"
        />

        <BaseInput
            v-if="listingType === 'agency'"
            label="Agency Name"
            v-model="formData.agencyName"
        />

        <BaseInput label="LinkedIn URL" v-model="formData.linkedIn" />

        <!-- GROUP 6: Navigation Buttons -->
        <div class="flex justify-between gap-8">
          <BaseButton
              :isPrimary="false"
              @click="back"
          >
            Back
          </BaseButton>
          <BaseButton :isPrimary="true" @click="nextStep">
            Next
          </BaseButton>
        </div>
      </div>
    </div>
  </StepPanel>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseInput from '../../common/InputFields/BaseInput.vue'
import BaseButton from '../../common/InputFields/BaseButton.vue'
import CountryDropdown from "../../common/InputFields/CountryDropdown.vue";
import StepPanel from "./StepPanel.vue";

const router = useRouter()
const listingType = ref<'freelancer' | 'agency' | null>(null)

onMounted(() => {
  const storedType = localStorage.getItem('listingType')
  if (storedType === 'freelancer' || storedType === 'agency') {
    listingType.value = storedType
  } else {
    router.push('/expert/signup')
  }
})

const formData = reactive({
  firstName: '',
  lastName: '',
  email: '',
  country: '',
  website: '',
  agencyName: '',
  linkedIn: '',
})

const nextStep = () => {
  localStorage.setItem('contactInfo', JSON.stringify(formData))
  router.push('/expert/signup/professional-details')
}
const back = () => {
  router.push('/expert/signup')
}
</script>
