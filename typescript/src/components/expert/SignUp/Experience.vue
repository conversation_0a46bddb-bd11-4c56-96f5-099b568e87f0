<template>
  <StepPanel>
    <div class="flex flex-col gap-8">
      <div class="flex flex-col gap-4">
        <div class="flex flex-row items-center justify-between">
          <h4 class="font-normal font-archivo text-primary tracking-10p">
            {{ listingType === 'freelancer' ? 'CREATE FREELANCER LISTING' : 'CREATE AGENCY LISTING' }}
          </h4>
          <h4 class="font-normal font-archivo text-primary tracking-10p">3/3</h4>
        </div>
        <h1 class="font-normal text-primary">You’re almost done!</h1>
        <p class="text-primary">
          We'd love to hear about your experience. Feel free to share your tech
          stack, apps you use daily, cool projects you've worked on – anything <br>
          you'd like us to know.
        </p>
      </div>

      <div class="flex flex-col gap-8">
        <BaseInput
            label="Share a short bio (Optional)"
            v-model="formData.bio"
            textarea
            :rows="6"
        />

        <!-- Navigation Buttons -->
        <div class="flex justify-between gap-8">
          <BaseButton :isPrimary="false" @click="goToPrevious">
            Back
          </BaseButton>
          <BaseButton :isPrimary="true" @click="submitForm">
            Submit Application
          </BaseButton>
        </div>
      </div>
    </div>
  </StepPanel>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue'
import { useRouter } from 'vue-router'
import BaseInput from '../../common/InputFields/BaseInput.vue'
import BaseButton from '../../common/InputFields/BaseButton.vue'
import StepPanel from "./StepPanel.vue";

const router = useRouter()

const listingType = ref<'freelancer' | 'agency'>('freelancer')

onMounted(() => {
  const type = localStorage.getItem('listingType')
  if (type === 'freelancer' || type === 'agency') {
    listingType.value = type
  } else {
    // fallback to start
    router.push('/expert/signup')
  }

  const saved = localStorage.getItem('professionalDetails')
  if (saved) Object.assign(formData, JSON.parse(saved))
})

const formData = reactive({
  bio: ''
})

const submitForm = () => {
  localStorage.setItem('experience', JSON.stringify(formData))
  router.push('')
}

const goToPrevious = () => {
  router.push('/expert/signup/professional-details')
}
</script>
