<template>
  <header class="flex items-center justify-between p-4 border-b bg-white">
    <div class="text-xl font-bold flex items-center">
      <Logo />
    </div>

    <div class="flex items-center gap-4">
<!--      <div class="relative">-->
<!--        <button class="p-2 rounded-sm hover:bg-muted">-->
<!--          <Bell />-->
<!--        </button>-->
<!--        <h5 v-if="notificationCount > 0" class="absolute top-0 right-0 bg-red-500 text-white rounded-full">-->
<!--          {{ notificationCount }}-->
<!--        </h5>-->
<!--      </div>-->

      <div class="relative">
        <button class="p-2 rounded-sm hover:bg-muted">
          <Bell />
        </button>
        <h5 v-if="notificationCount > 0"
            class="absolute top-0 right-0 w-4 h-4 text-[11px] bg-red-500 text-white rounded-full flex items-center justify-center">
          {{ notificationCount }}
        </h5>
      </div>


      <div class="relative">
        <button class="p-2 rounded-sm hover:bg-muted">
          <Message />
        </button>
        <h5 v-if="messageCount > 0" class="absolute top-0 right-0 w-4 h-4 text-[11px] bg-red-500 text-white rounded-full flex items-center justify-center">
          {{ messageCount }}
        </h5>
      </div>

      <button
          class="flex items-center py-1 pl-3 pr-2 hover:bg-muted bg-transparent border-none"
      >
        <span class="mr-2" v-if="!isOnboarding">My Profile</span>

        <template v-if="isOnboarding">
          <div
              class="w-8 h-8 rounded-full bg-black text-white flex items-center justify-center font-semibold text-sm select-none"
          >
            JK
          </div>
        </template>
        <template v-else>
          <img
              :src="profileImage ?? undefined"
              alt="Profile"
              class="w-8 h-8 rounded-sm object-cover"
          />
        </template>
      </button>
    </div>
  </header>
</template>

<script setup lang="ts">
import Logo from '../../assets/icons/logo.svg';
import Bell from '../../assets/icons/bell.svg';
import Message from '../../assets/icons/message.svg';

defineProps<{
  messageCount: number
  notificationCount: number
  profileImage: string | null
  isOnboarding?: boolean
}>()
</script>
