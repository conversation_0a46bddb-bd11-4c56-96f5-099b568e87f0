<template>
  <div class="pb-6 mt-14">
    <h3 class="font-semibold pb-2">Your Reviews</h3>
    <h4>Check what reviews potential clients will see on your profile.</h4>
  </div>

  <div>
    <ReviewCard
      v-for="(review, index) in yourReviews"
      :key="index"
      :review="review"
      isExpert
    />
  </div>
</template>

<script setup lang="ts">

import {ref} from "vue";
import ReviewCard from "../common/cards/ReviewCard.vue";

const yourReviews = ref([
  {
    reviewer: {
      id: 1,
      displayUrl: "https://randomuser.me/api/portraits/men/79.jpg",
      name: "<PERSON>",
      storeTitle: "SuperSport",
      recurringClient: true,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "Working with <PERSON><PERSON><PERSON> was a game-changer for our mobile store experience. The redesigned product grid is stunning and works flawlessly. Our mobile conversions have increased by 35%, thanks to his expertise!",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: true,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Organic",
    responses: [],
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/men/19.jpg",
    }
  },
  {
    reviewer: {
      id: 2,
      displayUrl: "https://randomuser.me/api/portraits/women/71.jpg",
      name: "Olivia R.",
      storeTitle: "Archery",
      recurringClient: false,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "Lautaro built an advanced filtering system that completely transformed how our customers shop. It’s fast, intuitive, and beautifully integrated into our site. His attention to detail is unmatched.",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: false,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Invited",
    responses: [],
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/women/11.jpg",
    }
  },
  {
    reviewer: {
      id: 3,
      displayUrl: "https://randomuser.me/api/portraits/men/76.jpg",
      name: "Jack R.",
      storeTitle: "United by Blue",
      recurringClient: false,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "We wanted a feature that let customers preview products in real-time, and Lautaro delivered beyond our expectations. The extension is seamless and our customers love it. He’s a Shopify wizard!",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: true,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Invited",
    responses: [],
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/men/16.jpg",
    }
  },
]);

</script>
