<template>
  <div class="mb-4 bg-white border rounded-md shadow-sm p-card-padding">
    <!-- Top row -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <p class="px-2 py-1 text-sm font-medium rounded text-primary">
          {{ type === 'direct' ? 'Direct Message' : 'Quote Request' }}
        </p>
        <h6 v-if="type === 'quote'" class="bg-blue-100 text-blue-600 font-semibold px-2 py-0.5 rounded">
          Offer Sent
        </h6>
      </div>
      <h5 class="text-sm font-normal text-primary md:text-base">Submitted on {{ submittedDate }}</h5>
    </div>

    <!-- Desktop Layout -->
    <div class="justify-between hidden gap-4 md:flex">
      <div class="flex items-start gap-4">
        <img :src="avatarUrl" alt="Avatar" class="object-cover w-12 h-12 rounded-full" />

        <div class="flex flex-wrap items-center gap-4 text-h4">
          <div class="flex flex-col">
            <p class="font-normal">{{ name }}</p>
            <h4 class="text-gray-500">{{ email }}</h4>
          </div>

          <div class="h-8 mx-2 border-l"></div>

          <a :href="storeUrl" target="_blank" class="flex items-center gap-1 text-h4 text-link hover:underline">
            {{ storeName }}
            <ExternalLink />
          </a>

          <div class="h-8 mx-2 border-l"></div>

          <h4>
            Shopify plan: {{ shopifyPlan }}
          </h4>

          <template v-if="budget">
            <div class="h-8 mx-2 border-l"></div>
            <h4>
              Budget: {{ budget }}
            </h4>
          </template>
        </div>
      </div>

      <div class="flex items-center justify-center gap-2 mt-4">
        <select v-model="status" class="px-1 py-2 border rounded w-36 text-h4 hover:bg-gray-100">
          <option value="In Progress">In Progress</option>
          <option value="Completed">Completed</option>
          <option value="Closed">Closed</option>
        </select>
        <router-link
            :to="`/expert/lead/${1}/chatroom`"
            class="flex items-center gap-2 px-4 py-2 text-white rounded bg-primary text-h4 hover:bg-gray-800"
        >
          <Chat />
          <span>Chat Now</span>
        </router-link>
        <button class="flex items-center gap-2 px-4 py-2 border rounded text-h4 hover:bg-gray-100">
          <Quote />
          <span>Send Quote</span>
        </button>
      </div>
    </div>

  <!-- Mobile Layout -->
<div class="md:hidden font-archivo text-paragraph">
  <!-- User Info Section -->
  <div class="flex items-start gap-3 mb-4">
    <img :src="avatarUrl" alt="Avatar" class="flex-shrink-0 object-cover w-16 h-16 rounded-full" />
    <div class="flex-1 min-w-0">
      <p class="font-medium text-h4">{{ name }}</p>
      <p class="break-all text-custom1 text-grey-light">{{ email }}</p>
    </div>
  </div>

  <div class="mb-4 space-y-2">
    <div class="flex items-center justify-between">
      <span class="text-custom1 text-greyDark">Shopify store</span>
      <a :href="storeUrl" target="_blank" class="flex items-center gap-1 text-custom1 text-link hover:underline">
        {{ storeName }}
        <ExternalLink class="w-3 h-3" />
      </a>
    </div>
    <div class="flex items-center justify-between">
      <span class="text-custom1 text-greyDark">Shopify plan:</span>
      <span class="font-medium text-custom1">{{ shopifyPlan }}</span>
    </div>
    <template v-if="budget">
      <div class="flex items-center justify-between">
        <span class="text-custom1 text-greyDark">Budget:</span>
        <span class="font-medium text-custom1">{{ budget }}</span>
      </div>
    </template>
  </div>

  <!-- Status Dropdown -->
  <div class="mb-4">
    <select
      v-model="status"
      class="w-full px-4 py-2 border rounded-md text-custom1 border-grey-light hover:bg-veryLightGray"
    >
      <option value="In Progress">In Progress</option>
      <option value="Completed">Completed</option>
      <option value="Closed">Closed</option>
    </select>
  </div>

  <!-- Action Buttons -->
  <div class="flex gap-2">
    <router-link
      :to="`/expert/lead/${1}/chatroom`"
      class="flex items-center justify-center flex-1 gap-2 px-4 py-2 text-white rounded-md text-custom1 bg-primary hover:bg-greyExtraDark"
    >
      <Chat class="w-4 h-4" />
      <span>Chat Now</span>
    </router-link>
    <button
      class="flex items-center justify-center flex-1 gap-2 px-4 py-2 border rounded-md text-custom1 border-primary hover:bg-veryLightGray"
    >
      <Quote class="w-4 h-4" />
      <span>Send Quote</span>
    </button>
  </div>
</div>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Chat from '../../../assets/icons/chat.svg';
import Quote from '../../../assets/icons/quote-dark.svg';
import ExternalLink from '../../../assets/icons/externalLink.svg';

const props = defineProps<{
  name: string
  email: string
  storeName: string
  storeUrl: string
  shopifyPlan: string
  avatarUrl: string
  submittedDate: string
  initialStatus?: string
  budget?: string | null
  type?: string | null
}>()

const status = ref(props.initialStatus || 'In Progress')
</script>

<style scoped>
</style>