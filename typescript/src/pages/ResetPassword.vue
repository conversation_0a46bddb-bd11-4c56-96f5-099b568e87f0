<template>
  <ResetPassword
      title="Reset Account Password"
      subtitle="for the account denis@<EMAIL>"
      :backgroundImage="background"
      :step="step"
  >
    <template #form>
      <div class="flex flex-col gap-8">
        <!-- New Password -->
        <div>
          <label for="new-password" class="text-h5 font-sm text-primary">New Password</label>
          <PasswordInput id="new-password" v-model="newPassword" />
        </div>

        <!-- Confirm New Password -->
        <div>
          <label for="confirm-password" class="text-h5 font-sm text-primary">Confirm New Password</label>
          <PasswordInput id="confirm-password" v-model="confirmNewPassword" />
        </div>

        <!-- Email Field -->
        <BaseInput
            label="Email"
            type="email"
            placeholder="Enter your email"
            v-model="email"
        />

        <!-- Submit Button -->
        <BaseButton
            class="w-full text-primary text-h5"
            :isPrimary="true"
            @click="reset"
        >
          Reset Password
        </BaseButton>
      </div>
    </template>
  </ResetPassword>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseInput from '../components/common/InputFields/BaseInput.vue'
import BaseButton from '../components/common/InputFields/BaseButton.vue'
import PasswordInput from '../components/common/InputFields/PasswordInput.vue'
import ResetPassword from '../components/common/ResetPassword.vue'

const email = ref('')
const newPassword = ref('')
const confirmNewPassword = ref('')
const step = ref(0)

const background = new URL('../assets/icons/background.svg', import.meta.url).href

const reset = () => {
  // Implement reset password logic here
}
</script>
