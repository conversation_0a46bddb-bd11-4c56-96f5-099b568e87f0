<script setup lang="ts">

import { ref } from "vue";
import ReviewCard from "../../components/common/cards/ReviewCard.vue";
import Search from "../../assets/icons/search.svg";

const status = ref('')
const rating = ref('')
const likelyToRecommend = ref('')
const projectValue = ref('')
const reviewSource = ref('')
const searchQuery = ref('')

const reviews = ref([
  {
    reviewer: {
      id: 1,
      displayUrl: "https://randomuser.me/api/portraits/men/79.jpg",
      name: "<PERSON>",
      storeTitle: "SuperSport",
      recurringClient: true,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "Working with Lautar<PERSON> was a game-changer for our mobile store experience. The redesigned product grid is stunning and works flawlessly. Our mobile conversions have increased by 35%, thanks to his expertise!",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: true,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Organic",
    responses: [],
    status: 'Pending Review',
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/men/19.jpg",
    }
  },
  {
    reviewer: {
      id: 2,
      displayUrl: "https://randomuser.me/api/portraits/women/71.jpg",
      name: "Olivia R.",
      storeTitle: "Archery",
      recurringClient: false,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "Lautaro built an advanced filtering system that completely transformed how our customers shop. It’s fast, intuitive, and beautifully integrated into our site. His attention to detail is unmatched.",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: false,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Invited",
    responses: [],
    status: 'Approved',
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/women/11.jpg",
    }
  },
  {
    reviewer: {
      id: 3,
      displayUrl: "https://randomuser.me/api/portraits/men/76.jpg",
      name: "Jack R.",
      storeTitle: "United by Blue",
      recurringClient: false,
      storeUrl: "https://www.trustpilot.com/",
      rating: "5.0",
      review: "We wanted a feature that let customers preview products in real-time, and Lautaro delivered beyond our expectations. The extension is seamless and our customers love it. He’s a Shopify wizard!",
      likelyToRecommend: "Very Likely",
      isShopexpertUser: true,
    },
    postedAt: "Dec 10, 2025",
    projectValue: "$1000-$2000",
    reviewSource: "Invited",
    responses: [],
    status: 'Approved',
    expert: {
      name: 'Expert Name',
      storeUrl: "https://www.trustpilot.com/",
      storeTitle: "OasisCofee",
      displayUrl: "https://randomuser.me/api/portraits/men/16.jpg",
    }
  },
]);
</script>

<template>
  <main class="flex-1 p-8 overflow-y-auto bg-secondary font-light space-y-8">
    <div class="flex flex-row justify-between">
      <h1>
        Reviews <span class="text-gray-500">({{ reviews.length }})</span>
      </h1>

      <div class="flex items-center border border-grey rounded-sm bg-white py-1 px-3 w-[300px] max-w-md shadow-sm">
        <Search />
        <input
            type="text"
            placeholder="Search Reviews ..."
            class="w-full ml-3 text-h4 outline-none placeholder-tertiary"
            v-model="searchQuery"
        />
      </div>
    </div>

    <div class="text-paragraph space-x-3">
      <select v-model="status" class="border rounded-sm px-1 w-auto py-2 text-h4 hover:bg-gray-100">
        <option value="">Status: All</option>
        <option value="In Progress">In Progress</option>
        <option value="Completed">Completed</option>
        <option value="Closed">Closed</option>
      </select>

      <select v-model="rating" class="border rounded-sm px-1 w-auto py-2 text-h4 hover:bg-gray-100">
        <option value="">Rating: All</option>
        <option value="freelance">Freelance</option>
        <option value="agency">Agency</option>
      </select>

      <select v-model="likelyToRecommend" class="border rounded-sm px-1 w-auto py-2 text-h4 hover:bg-gray-100">
        <option value="">Likely To Recommend: All</option>
        <option value="free">free</option>
        <option value="paid">Paid</option>
      </select>

      <select v-model="projectValue" class="border rounded-sm px-1 w-auto py-2 text-h4 hover:bg-gray-100">
        <option value="">Project Value: All</option>
        <option value="developer">Developer</option>
        <option value="designer">Designer</option>
      </select>

      <select v-model="reviewSource" class="border rounded-sm px-1 w-auto py-2 text-h4 hover:bg-gray-100">
        <option value="">Review Source: All</option>
        <option value="united_states">United Sates</option>
        <option value="canada">Canada</option>
      </select>
    </div>

    <div>
      <ReviewCard
          v-for="(review, index) in reviews"
          :key="index"
          :review="review"
          isAdmin
      />
    </div>
    
  </main>
</template>
