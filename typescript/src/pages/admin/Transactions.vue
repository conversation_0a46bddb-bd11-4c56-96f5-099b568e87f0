<script setup lang="ts">
import type {ITransaction} from '../../types.ts'
import { ref } from 'vue'
import Search from "../../assets/icons/search.svg"
import TransactionCard from "../../components/admin/cards/TransactionCard.vue";

const searchQuery = ref('')

const transactions = ref<ITransaction[]>([
  {
    type: 'Expert Premium Subscription',
    paymentMethod: 'Direct Payment',
    paymentDate: '10 Dec, 2025',
    transactionAmount: '$950.00',
    expert: {
      name: '<PERSON>',
      email: 'jero<PERSON><EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/44.jpg',
    },
  },
  {
    type: 'Project Quote',
    paymentMethod: 'Direct Payment',
    paymentDate: '10 Dec, 2025',
    transactionAmount: '$950.00',
    client: {
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/33.jpg',
      plan: 'Advanced',
    },
    expert: {
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/55.jpg',
    },
  },
  {
    type: 'Add to Scope',
    paymentMethod: 'Direct Payment',
    paymentDate: '10 Dec, 2025',
    transactionAmount: '$950.00',
    client: {
      name: 'Albert Flores',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      plan: 'Plus',
    },
    expert: {
      name: 'Guy Hawkins',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/41.jpg',
    },
  },
  {
    type: 'Prepaid Hours',
    paymentMethod: 'Direct Payment',
    paymentDate: '10 Dec, 2025',
    transactionAmount: '$950.00',
    prepaidHours: '25 hour pack',
    client: {
      name: 'Albert Flores',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      plan: 'Plus',
    },
  },
])

</script>

<template>
  <main class="flex-1 p-8 overflow-y-auto bg-gray-50 font-light space-y-8">
    <div class="flex justify-between items-center">
      <h1>
        Transactions
      </h1>
      <div class="flex items-center border border-grey rounded-sm bg-white py-1 px-3 w-[300px] max-w-md shadow-sm">
        <Search />
        <input
            type="text"
            placeholder="Search Transactions ..."
            class="w-full ml-3 text-h4 outline-none placeholder-tertiary"
            v-model="searchQuery"
        />
      </div>
    </div>
    <div class="space-y-4">
      <TransactionCard v-for="(transaction, index) in transactions" :key="index" :transaction="transaction" />
    </div>
  </main>
</template>
