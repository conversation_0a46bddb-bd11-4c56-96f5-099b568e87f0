<template>
  <main class="flex-1 p-8 overflow-y-auto bg-secondary font-light">
    <div class="flex flex-row mb-14 justify-between">
      <div>
        <h1>
          Welcome back, <span class="italic font-besley">Filip!</span>
        </h1>
        <p class="mt-2">
          This is overview of your shopexperts client dashboard.
        </p>
      </div>

      <button
          class="flex bg-primary text-white font-light rounded-sm py-1.5 h-fit px-2 items-center gap-1.5"
      >
        <span>Submit a Request</span>
        <Arrow />
      </button>

    </div>

    <TabNav
        :tabs="tabs"
    />
  </main>
</template>

<script setup lang="ts">
import TabNav from "../../components/TabNav.vue";
import LatestRequest from "../../components/client/LatestRequest.vue";
import ReviewRequests from "../../components/client/ReviewRequests.vue";
import Arrow from "../../assets/icons/arrow.svg";

const tabs = [
  { value: 'latest-requests', label: 'Latest Requests', component: LatestRequest },
  { value: 'latest-reviews', label: 'Reviews Requests', component: ReviewRequests },
]
</script>
