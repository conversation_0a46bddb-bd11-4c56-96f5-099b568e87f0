<template>
  <div class="flex-1 overflow-y-auto font-light bg-secondary p-8">
    <!-- Header Section -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8 space-y-4 md:space-y-0">
      <div>
        <h1 class="font-sm text-primary">My Requests</h1>
        <p class="text-primary font-sm">
          Easily manage all your project requests in one place.<br/>Stay organized and move your projects forward faster.
        </p>
      </div>
      <button
          class="flex bg-primary text-white font-light rounded-md h-fit py-2 px-4 items-center gap-2"
          @click=""
      >
        <span>Submit a Request</span>
        <Arrow />
      </button>
    </div>
    <div>
      <ExpertMatchedRequestCard
          v-for="expert in matchedRequests"
          :key="expert.id"
          :expert="expert"
          class="flex flex-col gap-3"
      />
      <ExpertDirectMessageCard
          v-for="expert in directRequests"
          :key="expert.id"
          :expert="expert"
          class="flex flex-col gap-3"
      />
      <ExpertQuoteRequestCard
          v-for="quote in quoteRequests"
          :key="quote.id"
          :quote="quote"
          class="flex flex-col gap-3"
      />
    </div>

  </div>
</template>

<script setup lang="ts">
import Arrow from "../../assets/icons/arrow.svg"
import ExpertDirectMessageCard from "../../components/client/cards/ExpertDirectMessageCard.vue"
import ExpertQuoteRequestCard from "../../components/client/cards/ExpertQuoteRequestCard.vue"

import type { IExpertDirectMessage, IExpertMatched, IExpertQuote } from "../../types.ts"
import ExpertMatchedRequestCard from "../../components/client/cards/ExpertMatchedRequestCard.vue";

const directRequests: IExpertDirectMessage[] = [
  {
    id: 1,
    name: "Michael Oswald",
    role: "Senior Shopify Developer",
    avatarUrl: "https://randomuser.me/api/portraits/men/10.jpg",
    submittedDate: "17 Dec, 2025",
    initialStatus: "In Progress",
    rating: "5.00",
    numberOfReviews: 16,
    pendingQuote: "",
    request_id: 1,
    request_type: "Direct Message",
    developerRank: "Top Developer"
  }
]

const matchedRequests: IExpertMatched[] = [
  {
    id: 1,
    request_id: 1,
    request_type: "Matched",
    title: "Collection Page Changes & Updates on Product Page",
    name: "Michael Oswald",
    role: "Senior Shopify Developer",
    avatarUrl: "https://randomuser.me/api/portraits/men/10.jpg",
    submittedDate: "17 Dec, 2025",
    initialStatus: "In Progress",
    rating: "5.00",
    numberOfReviews: 16,
    pendingQuote: "",
    developerRank: "Top Developer"
  }
]

const quoteRequests: IExpertQuote[] = [
  {
    id: 1,
    request_id: 1,
    request_type: "Quote Request",
    title: "Collection Page Changes & Updates on Product Page",
    expert: {
      id: 1,
      name: "Michael Oswald",
      role: "Senior Shopify Developer",
      avatarUrl: "https://randomuser.me/api/portraits/men/9.jpg",
      submittedDate: "17 Dec, 2025",
      initialStatus: "In Progress",
      rating: "5.00",
      numberOfReviews: 16,
      pendingQuote: "",
    },
    additionalExperts: [
      {
        id: 1,
        name: "Michael Oswald",
        role: "Senior Shopify Developer",
        avatarUrl: "https://randomuser.me/api/portraits/men/8.jpg",
        submittedDate: "17 Dec, 2025",
        initialStatus: "In Progress",
        rating: "5.00",
        numberOfReviews: 16,
        pendingQuote: "Submitted"
      },
      {
        id: 2,
        name: "Michael Oswald",
        role: "Senior Shopify Developer",
        avatarUrl: "https://randomuser.me/api/portraits/men/9.jpg",
        submittedDate: "17 Dec, 2025",
        initialStatus: "In Progress",
        rating: "5.00",
        numberOfReviews: 16,
        pendingQuote: "Pending"
      },
      {
        id: 3,
        name: "Michael Oswald",
        role: "Senior Shopify Developer",
        avatarUrl: "https://randomuser.me/api/portraits/men/8.jpg",
        submittedDate: "17 Dec, 2025",
        initialStatus: "In Progress",
        rating: "5.00",
        numberOfReviews: 16,
        pendingQuote: "Submitted"
      }
    ]
  },
  {
    id: 2,
    title: "Add Custom Sections on Homepage",
    request_id: 2,
    request_type: "Quote Request",
    expert: {
      id: 2,
      name: "Sophie Martin",
      role: "Shopify UI/UX Expert",
      avatarUrl: "https://randomuser.me/api/portraits/women/12.jpg",
      submittedDate: "20 Dec, 2025",
      initialStatus: "In Progress",
      rating: "4.90",
      numberOfReviews: 22,
      pendingQuote: "Submitted",
      hourlyRate: 90,
      estimatedTime: "8 hours",
      deadline: "28 Dec, 2025",
      totalToPay: "$720.00",
      quoteStatus: "Submitted"
    }
  }
];
</script>
