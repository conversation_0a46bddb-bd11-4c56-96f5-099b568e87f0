<template>
  <main class="flex-1 p-6 overflow-y-auto bg-secondary font-light">
    <div class="grid grid-cols-[75%_1fr] h-screen bg-secondary py-10 px-5 gap-x-5 w-full">
      <div>
        <div class="flex flex-row mb-6 justify-between">
          <div class="flex items-center gap-2">
            <BackButton/>
            <h2 v-if="route.params.type !== 'Direct Message'">
              Collection Page Changes
            </h2>
            <h2 v-else>
              Direct Message / <PERSON>
            </h2>
          </div>
        </div>
        <TabNav
            v-if="route.params.type !== 'Direct Message'"
            :tabs="tabs"
        />
        <Chatroom v-else />
      </div>
      <ExpertDetails
          @show-project-quote="isShowProjectQuoteModal = true"
          :type="selectedType"
      />
    </div>
  </main>
  <ProjectQuote
      v-if="isShowProjectQuoteModal"
      @close="isShowProjectQuoteModal = false"
  />
</template>

<script setup lang="ts">
import TabNav from "../../components/TabNav.vue";
import QuoteRequest from "../../components/common/QuoteRequest.vue";
import Chatroom from "../../components/common/Chatroom.vue";
import Invoices from "../../components/common/Invoices.vue";
import StatusHistory from "../../components/common/StatusHistory.vue";
import BackButton from "../../assets/icons/back-button.svg"
import ProjectQuote from "../../components/expert/modals/ProjectQuote.vue";
import {ref, computed} from "vue";
import ExpertDetails from "../../components/common/cards/ExpertDetails.vue";
import { useRoute } from 'vue-router'

const route = useRoute()

const selectedType = computed(() => {
  const typeParam = route.params.type
  if (typeParam === 'Quote Request' || typeParam === 'Matched' || typeParam === 'Direct Message') {
    return typeParam
  }
  return 'Quote Request' // Fallback default
})

const tabs = [
  { value: 'quote-request', label: 'Quote Request', component: QuoteRequest, props: { isClientSide: true } },
  { value: 'chatroom', label: 'Chatroom', component: Chatroom },
  { value: 'invoices', label: 'Invoices', component: Invoices },
  { value: 'status-history', label: 'StatusHistory', component: StatusHistory },
]
const isShowProjectQuoteModal = ref<boolean>(false);


</script>
