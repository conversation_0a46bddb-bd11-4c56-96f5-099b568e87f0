import type {Component} from "vue";

export interface IButton {
    title?: string
    icon?: string | Component
    iconPosition?: 'left' | 'right'
    variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'icon'
    size?: 'sm' | 'md' | 'lg' | 'icon'
    rounded?: 'sm' | 'md' | 'full'
    fullWidth?: boolean
    ariaLabel?: string
    type?: 'button' | 'submit' | 'reset'
}

export interface INav {
    label: string
    icon: string
    path: string
}

export interface IDropdown {
    label: string
    value: string
}

export interface ILead {
    id: number,
    name: string,
    displayUrl: string,
    website: string,
    email: string,
    plan: string,
    directChatCount: number,
    quoteRequestCount: number,
    lifetimeSpendCount: string,
    joinedOn: string,
}

export interface IListing {
    id: number,
    name: string
    displayUrl: string
    type: string
    email: string
    storeTitle: string
    storeUrl: string
    country: string
    jobTitle: string
    language: string
    minimumProjectBudget: string
    status: string
    statusUpdatedAt: string
    servicesOffered: string[]
}

export interface IReview {
    reviewer: {
        id: number,
        name: string,
        displayUrl: string,
        storeTitle: string,
        storeUrl: string,
        recurringClient: boolean,
        rating: string,
        review: string,
        likelyToRecommend: string,
        isShopexpertUser: boolean,
    }
    postedAt: string,
    projectValue: string,
    reviewSource: string,
    responses: string[],
    status?: string
    expert: {
        name: string,
        storeUrl: string,
        storeTitle: string,
        displayUrl: string,
        rank?: string,
        type?: string,
        recurringExpert?: boolean,
        isShopexpertUser?: boolean,
    }
}

export interface ITransaction {
    type: string
    paymentMethod: string
    paymentDate: string
    transactionAmount: string
    prepaidHours?: string
    client?: {
        name: string
        email: string
        avatar: string
        plan: string
    }
    expert?: {
        name: string
        email: string
        avatar: string
    }
}

export interface IInvoice {
    type: string
    paymentMethod: string
    paymentDate: string
    transactionAmount: string
    isStandardRate: boolean,
}

export interface IStatusHistory {
    description: string,
    time: string,
}

export interface IQuote {
    title: string
    link: string
    hourlyRate: string
    estimatedTime: string
    deadline: string
    total: string
    status: string
    sentDate: string
    paidDate?: string
    rejectedDate?: string
    client: {
        name: string
        email: string
        avatar: string
        plan: string
    }
    expert: {
        name: string
        email: string
        avatar: string
    }
}

export interface IReferral {
    referrer: {
        name: string
        email: string
        avatar: string
    }
    referral: {
        name: string
        email: string
        avatar: string
        shopifyPlan: string
    }
    createdAccount: string
    completedProject: string
    amount: string
    status: string
    referredOn: string
    approvedOn?: string | null
    rejectedOn?: string | null
}

export interface ILeaderboard {
    rank: number
    name: string
    role: string
    rating?: string
    reviews?: number
    avatar: string
    responseTime?: string
}

export interface ICollectionQuote {
    id: number
    type: string
    hourlyRate: number
    status: string
    estimatedTime: string
    deadline: string
    totalPayment: string
    createdAt: string
}

export interface IExpert {
    id?: number;
    name: string;
    role: string;
    avatarUrl: string;
    submittedDate: string;
    initialStatus?: string;
    rating?: string;
    numberOfReviews?: number;
    pendingQuote?: string;
}

export interface IExpertMatched extends IExpert {
    title: string;
    request_id: number;
    request_type: string;
    developerRank: string;
}

export interface IExpertDirectMessage extends IExpert {
    request_id: number;
    request_type: string;
    developerRank: string;
}

export interface IExpertQuote {
    id: number;
    title: string;
    request_id: number;
    request_type: string;
    expert: IExpertQuoteItem;
    additionalExperts?: IExpertQuoteItem[];
}

export interface IFeaturedExpert extends IExpert {
    startingPrice: string | number
    services: string[]
}

export interface IFeaturedService {
    id: number
    title: string
    image: string
    deliveryTime: string
    price: number
}

export interface IExpertQuoteItem extends IExpert {
    hourlyRate?: number; // ← optional
    estimatedTime?: string;
    deadline?: string;
    totalToPay?: string;
    quoteStatus?: string;
}

export interface IMessage {
    id: number
    content: string
    createdAt: string
    project: {
        title: string
    }
    expert: {
        name: string
        avatarUrl: string
    }
}

export interface INotification {
    id: number
    content: string
    createdAt: string
    project: {
        title: string
    }
}