{"name": "vue", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve --mode dev", "staging": "vue-cli-service build --mode staging", "build": "vue-cli-service build --mode local", "lint": "vue-cli-service lint"}, "dependencies": {"@bugsnag/browser-performance": "^2.9.1", "@ownego/polaris-vue": "^2.0.0", "@stripe/stripe-js": "^4.0.0", "@vue-stripe/vue-stripe": "^4.5.0", "@vuepic/vue-datepicker": "^9.0.3", "@vueup/vue-quill": "^1.2.0", "axios": "^1.6.8", "core-js": "^3.8.3", "js-cookie": "^3.0.5", "laravel-echo": "^1.16.1", "moment": "^2.30.1", "pusher-js": "^8.4.0-rc2", "qs": "^6.13.0", "uuid": "^10.0.0", "vue": "^3.2.13", "vue-advanced-cropper": "^2.8.9", "vue-router": "^4.3.0", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-svg-loader": "^0.17.0-beta.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}