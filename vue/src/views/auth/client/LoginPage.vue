<script>
import ViewIcon from '../../../components/icons/ViewIcon';
import HideIcon from '../../../components/icons/HideIcon';
import CheckIcon from "@/components/icons/CheckIcon.vue";
import XIcon from "@/components/icons/XIcon.vue";
import LoginLayout from "@/layout/LoginLayout.vue";
import axios from "axios";
import InputBtn from "@/components/misc/InputBtn.vue";
import socket from "@/mixins/socket";

export default {
  name: "LoginPage",

  components: {
    InputBtn,
    LoginLayout
  },

  mixins: [socket],

  data() {
    return {
      ViewIcon,
      HideIcon,
      CheckIcon,
      XIcon,
      loading: false,

      passHidden: true,

      form: {
        email: '',
        password: '',
        confirm_password: '',
      },

      errors: {
        email: null,
        password: null,
      },

      error: '',

      mailSent: false,
    }
  },

  methods: {
    async tryLogin() {
      this.error = '';
      this.errors = {
        email: null,
        password: null,
      }

      this.loading = true;

      await axios.post('api/login', {email: this.form.email, password: this.form.password, role: 'client'}).then(res => {
        window.localStorage.setItem('CURRENT_USER', JSON.stringify(res.data.user))
        window.localStorage.setItem('CURRENT_TOKEN', res.data.token)

        axios.defaults.headers.common['Authorization'] = 'Bearer ' + res.data.token;
        this.initializeSocket(res.data.token);
        this.loading = false;

        this.$router.push('/client')
      }).catch(err => {
        this.loading = false;

        this.errors = err.response.data.errors ?? {email: null, password: null};
        this.error = err.response.data.message;
      });
    },

    showPass() {
      this.passHidden = !this.passHidden;
    },

    removeNotification() {
      this.mailSent = false;
    }
  }
}
</script>

<template>
  <LoginLayout>
    <BlockStack gap="100">
      <Text variant="headingXl" fontWeight="bold" as="h2">
        Client Login
      </Text>
      <Text variant="bodyLg" as="p" alignment="start" tone="subdued">
        Continue to Shopexperts profile.
      </Text>
    </BlockStack>

    <Badge tone="success" size="large" v-if="mailSent" style="height: 36px">
      <InlineStack gap="200" blockAlign="center" align="start" padding="200">
        <Icon :source="CheckIcon" />
        <InlineStack align="space-between" style="width: 345px" :wrap="false">
          <Text variant="bodyLg" as="p" alignment="start" tone="success">
            A password reset link has been emailed to you.
          </Text>

          <div>
            <Icon :source="XIcon" @click="removeNotification" />
          </div>
        </InlineStack>
      </InlineStack>
    </Badge>

    <Badge tone="critical" size="large" v-if="error" style="height: 36px">
      <InlineStack gap="200" blockAlign="center" align="start" padding="200">
        <InlineStack align="space-between" style="width: 345px" :wrap="false">
          <Text variant="bodyLg" as="p" alignment="start" tone="critical">
            {{ error }}
          </Text>
        </InlineStack>
      </InlineStack>
    </Badge>

    <BlockStack gap="600">
      <TextField
          type="email"
          label="Email"
          autoComplete="email"
          v-model="form.email"
          placeholder="Enter your email"
          :error="errors.email ? errors.email[0] : null"
          @keyup.enter="tryLogin"
      />

      <TextField
          :type="passHidden ? 'password' : 'text'"
          label="Password"
          v-model="form.password"
          placeholder="Enter your password"
          :label-action="{content: 'Forgot password?', onAction: () => {this.$router.push('/expert/forgot-password')}}"
          autoComplete="off"
          :error="errors.password ? errors.password[0] : null"
          @keyup.enter="tryLogin"
      >
        <template v-slot:suffix>
          <Icon v-if="passHidden" :source="ViewIcon" tone="base" @click="showPass" style="cursor: pointer"/>
          <Icon v-else :source="HideIcon" tone="base" @click="showPass" style="cursor: pointer"/>
        </template>
      </TextField>
    </BlockStack>

    <BlockStack gap="600">
      <InputBtn @click="tryLogin" :loading="loading">Login</InputBtn>

      <Text variant="bodyMd" as="p" alignment="center" tone="subdued">
        Don't have account?
        <Link :removeUnderline="true" external target="_blank" to="/client/register" >Sign up</Link>
      </Text>
    </BlockStack>
  </LoginLayout>
</template>

<style scoped>

</style>
